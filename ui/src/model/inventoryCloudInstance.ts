/**
 * Generated by orval v7.11.2 🍺
 * Do not edit manually.
 * Engage API
 * OpenAPI spec version: 2.0.0
 */
import type { InventoryCloudInstanceCiDeploymentStatus } from "./inventoryCloudInstanceCiDeploymentStatus";
import type { InventoryCloudInstanceCloudInstanceState } from "./inventoryCloudInstanceCloudInstanceState";
import type { InventoryCloudInstanceOpenPorts } from "./inventoryCloudInstanceOpenPorts";
import type { InventoryCloudInstancePublicIpv4Address } from "./inventoryCloudInstancePublicIpv4Address";

export interface InventoryCloudInstance {
  ci_deployment_status: InventoryCloudInstanceCiDeploymentStatus;
  client_name: string;
  cloud_instance_id: string;
  cloud_instance_state: InventoryCloudInstanceCloudInstanceState;
  created_at: string;
  id: string;
  name: string;
  open_ports: InventoryCloudInstanceOpenPorts;
  operating_system_image_id: string;
  provider: string;
  public_ipv4_address: InventoryCloudInstancePublicIpv4Address;
  region: string;
  title: string;
  type: string;
  updated_at: string;
}
