import {
  faBox,
  faChevronDown,
  faClipboard<PERSON>ist,
  faGear,
  faGlobe,
  shield-halved,
} from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  Disclosure,
  DisclosureButton,
  DisclosurePanel,
} from "@headlessui/react";
import { Link, useLocation } from "@tanstack/react-router";
import { AiFillSecurityScan } from "react-icons/ai";

import { useRoles } from "../context/useRoles";

export default function SideNav() {
  const roles = useRoles();
  const location = useLocation();

  const isPathActive = (path: string, exact: boolean = false) => {
    if (exact) {
      return location.pathname === path;
    }
    return location.pathname.startsWith(path);
  };

  const isSecurityOpen = isPathActive("/security");

  const navItems = [
    { to: "/", label: "Engagements", icon: faGear, exact: true },
    { to: "/inventory", label: "Inventory", icon: faClipboardList },
    { to: "/domains", label: "Domains", icon: faGlobe },
    { to: "/deployments", label: "Deployments", icon: faBox },
  ];

  const securityItems = [
    { to: "/security/user-management", label: "User Management" },
    { to: "/security/script-management", label: "Script Management" },
    { to: "/security/archive-management", label: "Archive Management" },
    { to: "/security/assignment-logs", label: "Audit Logs" },
    { to: "/security/set-instance-types", label: "Set Instance Types" },
  ];

  const getLinkClassName = (path: string, exact: boolean = false) => {
    const active = isPathActive(path, exact);
    const baseClasses =
      "flex w-full items-center space-x-3 rounded-lg p-2 text-left font-medium transition-colors duration-200";

    if (active) {
      return `${baseClasses} bg-purple-100 dark:bg-slate-600 text-purple-700 dark:text-slate-100`;
    } else {
      return `${baseClasses} text-gray-700 dark:text-slate-300 hover:bg-gray-100 dark:hover:bg-slate-700`;
    }
  };

  return (
    <div className="dark:bg-darkbg h-screen w-64 flex-none border-r-2 border-solid border-slate-200 bg-white p-6 pt-0 md:pt-6 dark:border-slate-500 dark:text-slate-200">
      <div className="flex flex-row items-center justify-start space-x-2 pb-4 md:pb-8">
        <AiFillSecurityScan className="h-9 w-9 text-black! md:h-12 md:w-12 dark:text-white!" />
        <div className="text-xl font-bold md:text-2xl">Engage</div>
      </div>
      <div className="w-full space-y-1 text-slate-900 dark:text-slate-200">
        <div className="py-2 text-sm font-semibold text-gray-400 uppercase">
          Menu
        </div>
        {navItems.map((item) => (
          <Link
            key={item.to}
            to={item.to}
            className={getLinkClassName(item.to, item.exact)}
          >
            <FontAwesomeIcon icon={item.icon} className="h-5 w-5" />
            <span>{item.label}</span>
          </Link>
        ))}

        {roles?.some((role) => role.includes("Admin")) ? (
          <Disclosure defaultOpen={isSecurityOpen} as="div">
            <DisclosureButton className={getLinkClassName("/security")}>
              <div className="flex flex-grow items-center space-x-3">
                <FontAwesomeIcon icon="shield-halved" className="h-5 w-5" />
                <span>Security</span>
              </div>
              <FontAwesomeIcon
                icon={faChevronDown}
                className={`${isSecurityOpen ? "rotate-180 transform" : ""} h-4 w-4 transition-transform`}
              />
            </DisclosureButton>
            <DisclosurePanel as="div" className="pt-1">
              <div className="ml-4 flex flex-col space-y-1">
                {securityItems.map((item) => (
                  <Link
                    key={item.to}
                    to={item.to}
                    className={getLinkClassName(item.to)}
                  >
                    <span className="pl-6">{item.label}</span>
                  </Link>
                ))}
              </div>
            </DisclosurePanel>
          </Disclosure>
        ) : null}
      </div>
    </div>
  );
}
