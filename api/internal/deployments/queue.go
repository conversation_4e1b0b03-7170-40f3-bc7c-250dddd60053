package deployments

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"net/netip"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/hashicorp/terraform-exec/tfexec"
	"github.com/jackc/pgx/v5/pgtype"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/converters"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/deployments/keys"
	"golang.org/x/crypto/ssh"
)

type Deployment struct {
	ID              string    `json:"id"`
	UserID          string    `json:"user_id"`
	EngagementID    string    `json:"engagement_id"`
	NodeID          string    `json:"node_id"`
	TerraformModule string    `json:"terraform_module"`
	CreatedAt       time.Time `json:"created_at"`
	AccountID       string    `json:"account_id"`
	Provider        string    `json:"provider"`
}

// cleanupTerraformDirectory removes the Terraform temporary directory after deployment operation
func cleanupTerraformDirectory(deploymentID string, logger *slog.Logger) {
	if deploymentID == "" {
		logger.Warn("Cannot cleanup Terraform directory: deployment ID is empty")
		return
	}

	err := os.RemoveAll(deploymentID)
	if err != nil {
		logger.Error("Failed to remove Terraform deployment directory",
			"deployment_id", deploymentID,
			"error", err.Error())
	} else {
		logger.Info("Successfully removed Terraform deployment directory",
			"deployment_id", deploymentID)
	}
}

func Worker(awsRootRegion string, secretKey string, queries *db.Queries, logger *slog.Logger, terraformExecPath string, taskBody []byte) error {

	var deployment Deployment
	err := json.Unmarshal(taskBody, &deployment)
	if err != nil {
		logger.Error("Failed to unmarshal deployment body")
		return err
	}

	logger = logger.With("deployment_id", deployment.ID, "created_at", deployment.CreatedAt, "node_id", deployment.NodeID, "engagement_id", deployment.EngagementID, "userID", deployment.UserID, "accountID", deployment.AccountID)

	deploymentIDPgType, err := converters.StringToPgTypeUUID(deployment.ID)
	if err != nil {
		logger.Error("Failed to convert deployment ID to pgtype.UUID")
		return err
	}

	logger.Info("Starting deployment task")

	err = queries.UpdateDeploymentStatus(context.Background(), db.UpdateDeploymentStatusParams{
		Status: db.DeploymentStatusEnumINPROGRESS,
		ID:     *deploymentIDPgType,
	})
	if err != nil {
		logger.Error("Error updating status of deployment task", "error", err.Error())
		_ = queries.SetDeploymentStatusToError(context.Background(), db.SetDeploymentStatusToErrorParams{
			ErrorMessage: pgtype.Text{String: err.Error(), Valid: true},
			ID:           *deploymentIDPgType,
		})
		return err
	}

	err = os.MkdirAll(deployment.ID, 0750) // No error if the directory already exists
	if err != nil {
		logger.Error("Failed to create Terraform directory", "error", err.Error())
		_ = queries.SetDeploymentStatusToError(context.Background(), db.SetDeploymentStatusToErrorParams{
			ErrorMessage: pgtype.Text{String: err.Error(), Valid: true},
			ID:           *deploymentIDPgType,
		})
		return err
	}

	// Schedule cleanup of Terraform temporary directory after operation completes
	defer cleanupTerraformDirectory(deployment.ID, logger)

	filePath := filepath.Join(deployment.ID, "main.tf")
	if err := os.WriteFile(filePath, []byte(deployment.TerraformModule), 0644); err != nil {
		logger.Error("Failed to create Terraform file", "error", err.Error())
		_ = queries.SetDeploymentStatusToError(context.Background(), db.SetDeploymentStatusToErrorParams{
			ErrorMessage: pgtype.Text{String: err.Error(), Valid: true},
			ID:           *deploymentIDPgType,
		})
		return err
	}

	tf, err := tfexec.NewTerraform(filepath.Join(deployment.ID), terraformExecPath)
	if err != nil {
		logger.Error("Error running Terraform", "error", err.Error())
		_ = queries.SetDeploymentStatusToError(context.Background(), db.SetDeploymentStatusToErrorParams{
			ErrorMessage: pgtype.Text{String: err.Error(), Valid: true},
			ID:           *deploymentIDPgType,
		})
		return err
	}

	// Initialize Terraform
	err = tf.Init(context.Background(), tfexec.Upgrade(true))
	if err != nil {
		logger.Error("Failed to initialise Terraform", "error", err.Error())
		_ = queries.SetDeploymentStatusToError(context.Background(), db.SetDeploymentStatusToErrorParams{
			ErrorMessage: pgtype.Text{String: err.Error(), Valid: true},
			ID:           *deploymentIDPgType,
		})
		return err
	}

	// Attempt to select a workspace using the Node ID, otherwise attempt to create new
	err = tf.WorkspaceSelect(context.Background(), deployment.NodeID)
	if err != nil {
		logger.Warn("Failed to select Terraform workspace with ID same as Node ID, attempting to create new", "error", err.Error())
		err = tf.WorkspaceNew(context.Background(), deployment.NodeID)
		if err != nil {
			logger.Error("Failed create new Terraform workspace with ID same as Node ID", "error", err.Error())
			_ = queries.SetDeploymentStatusToError(context.Background(), db.SetDeploymentStatusToErrorParams{
				ErrorMessage: pgtype.Text{String: err.Error(), Valid: true},
				ID:           *deploymentIDPgType,
			})
			return err
		}
	}

	// Create a new SecretsManager instance
	sm, err := keys.NewSecretsManager(awsRootRegion)
	if err != nil {
		logger.Error("Error initializing SecretsManager for Engagement", "error", err.Error())
		return err
	}

	secretValues, err := sm.GetSecret(deployment.AccountID)
	if err != nil {
		logger.Error("Failed to get secret for the Account", "error", err.Error())
		return err
	}

	err = tf.Apply(context.Background())
	if err != nil {
		logger.Error("Failed to apply Terraform configuration", "error", err.Error())
		_ = queries.SetDeploymentStatusToError(context.Background(), db.SetDeploymentStatusToErrorParams{
			ErrorMessage: pgtype.Text{String: err.Error(), Valid: true},
			ID:           *deploymentIDPgType,
		})
		return err
	}

	// Destroy the Terraform configuration
	//err = tf.Destroy(context.Background())
	//if err != nil {
	//	logger.Error("failed to destroy Terraform configuration", "error", err.Error())
	//	err = queries.SetDeploymentStatusToError(context.Background(), db.SetDeploymentStatusToErrorParams{
	//		ErrorMessage: pgtype.Text{String: err.Error(), Valid: true},
	//		ID:           deployment.ID,
	//	})
	//	return
	//}
	//
	//logger.Info("Terraform destroy complete", "deploymentID", deploymentIDString, "createdAt", deployment.CreatedAt.Time, "engagementID", engagementIDString, "nodeID", nodeIDString)

	logger.Info("Terraform deployment completed. Instance is in the cloud.")

	nodeIDPgType, _ := converters.StringToPgTypeUUID(deployment.NodeID)

	var addr netip.Addr
	var publicIpV4AddressSanitized string
	var instanceIdSanitized string

	// As Azure at some testing cases did not succeed to return output values on the first attempt,
	// the below is added to retry in those cases
	for i := 0; i < 10; i++ {
		err := tf.Refresh(context.Background())
		if err != nil {
			logger.Warn("Terraform refresh failed", "attempt", i+1, "error", err)
			time.Sleep(5 * time.Second)
			continue
		}
		output, err := tf.Output(context.Background())

		if err != nil {
			logger.Warn("Terraform output fetch failed", "attempt", i+1, "error", err)
			time.Sleep(5 * time.Second)
			continue
		}

		ipRaw := string(output["instance_public_ipv4"].Value)
		publicIpV4AddressSanitized = strings.Replace(ipRaw, "\"", "", -1)

		instanceIDRaw := string(output["instance_id"].Value)
		instanceIdSanitized = strings.Replace(instanceIDRaw, "\"", "", -1)

		if publicIpV4AddressSanitized != "" && instanceIdSanitized != "" {
			addr, err = netip.ParseAddr(publicIpV4AddressSanitized)
			if err != nil {
				logger.Info("Output values empty. Retrying to get output values for deployment",
					"deployment_id", deployment.ID,
					"error", err.Error(),
				)
			}
			if err == nil {
				break
			}
			logger.Warn("Invalid IP format", "ip", publicIpV4AddressSanitized, "error", err)
		}
		time.Sleep(5 * time.Second)
	}

	if !addr.IsValid() {
		_ = queries.SetDeploymentStatusToError(context.Background(), db.SetDeploymentStatusToErrorParams{
			ErrorMessage: pgtype.Text{String: err.Error(), Valid: true},
			ID:           *deploymentIDPgType,
		})
		return fmt.Errorf("failed to get a valid public IP after retries")
	}
	if instanceIdSanitized == "" {
		_ = queries.SetDeploymentStatusToError(context.Background(), db.SetDeploymentStatusToErrorParams{
			ErrorMessage: pgtype.Text{String: err.Error(), Valid: true},
			ID:           *deploymentIDPgType,
		})
		return fmt.Errorf("failed to get a valid instance id output after retries")
	}

	InstanceIdPg := pgtype.Text{String: instanceIdSanitized, Valid: true}

	// Store the public IP address and Cloud Instance ID in the database
	err = queries.UpdateCloudInstanceIpv4AddressAndID(context.Background(), db.UpdateCloudInstanceIpv4AddressAndIDParams{
		PublicIpv4Address: &addr,
		NodeID:            *nodeIDPgType,
		CloudInstanceID:   InstanceIdPg,
	})
	if err != nil {
		logger.Error("Error updating public IPv4 address or Cloudinstance ID in the database", "error", err.Error())
		_ = queries.SetDeploymentStatusToError(context.Background(), db.SetDeploymentStatusToErrorParams{
			ErrorMessage: pgtype.Text{String: err.Error(), Valid: true},
			ID:           *deploymentIDPgType,
		})
		return err
	}

	// Log IP address assignment during deployment
	userIDPgType, err := converters.StringToPgTypeUUID(deployment.UserID)
	if err == nil {
		timestamp := pgtype.Timestamp{
			Time:  time.Now(),
			Valid: true,
		}
		logMessage := fmt.Sprintf("Cloud instance deployed successfully, IP address assigned: %s (Instance ID: %s)", publicIpV4AddressSanitized, instanceIdSanitized)

		// Import the activitylogs package at the top of the file if not already imported
		err = queries.InsertActivityLog(context.Background(), db.InsertActivityLogParams{
			Message:   logMessage,
			Type:      db.LogsNodesTypeEnumNODECREATION,
			UserID:    *userIDPgType,
			NodeID:    *nodeIDPgType,
			CreatedAt: timestamp,
		})
		if err != nil {
			logger.Error("Error inserting deployment IP assignment log", "error", err.Error())
			// Continue execution even if logging fails
		}
	}

	engagementIDPgType, _ := converters.StringToPgTypeUUID(deployment.EngagementID)
	accountIDPgType, _ := converters.StringToPgTypeUUID(deployment.AccountID)

	// cloud provider dependent params
	var policyID, statusID string
	var userName string

	switch deployment.Provider {
	case "AWS":
		key, _ := queries.GetAccountSshPrivateKey(context.Background(), *accountIDPgType)
		policyID = key.PolicyID
		statusID = key.StatusID
		userName = "admin"

	case "AZURE":
		key, _ := queries.GetAzureTenantSshPrivateKey(context.Background(), *accountIDPgType)
		policyID = key.PolicyID
		statusID = key.StatusID
		userName = "azureuser"
	}

	decryptedSecret, err := keys.DecryptSecret(policyID, statusID, secretKey)

	if err != nil {
		logger.Error("Failed to decrypt SSH passphrase", "error", err.Error())
		_ = queries.SetDeploymentStatusToError(context.Background(), db.SetDeploymentStatusToErrorParams{
			ErrorMessage: pgtype.Text{String: err.Error(), Valid: true},
			ID:           *deploymentIDPgType,
		})
		return err
	}

	sshPrivateKey, found := secretValues["ssh_private_key"]
	if !found {
		logger.Error("Failed to get secret for Engagement")
		return fmt.Errorf("failed to get SSH private key for Engagement")
	}

	sshClient := keys.NewSSHClient(fmt.Sprintf("%s:22", strings.Replace(publicIpV4AddressSanitized, "\"", "", -1)), userName, sshPrivateKey, string(decryptedSecret), 10, 2*time.Second)

	logger = logger.With("public_ipv4_address", publicIpV4AddressSanitized)
	logger = logger.With("instance id", instanceIdSanitized)

	// Attempt to connect to the server
	if err := sshClient.Connect(); err != nil {
		logger.Error("SSH connection failed", "error", err.Error())
		_ = queries.SetDeploymentStatusToError(context.Background(), db.SetDeploymentStatusToErrorParams{
			ErrorMessage: pgtype.Text{String: err.Error(), Valid: true},
			ID:           *deploymentIDPgType,
		})
		return err
	}

	// Get the users of the Engagement
	engagementUsers, err := queries.GetEngagementUsers(context.Background(), *engagementIDPgType)
	if err != nil {
		logger.Error("Failed to get assigned users of the Engagement", "error", err.Error())
		_ = queries.SetDeploymentStatusToError(context.Background(), db.SetDeploymentStatusToErrorParams{
			ErrorMessage: pgtype.Text{String: err.Error(), Valid: true},
			ID:           *deploymentIDPgType,
		})
		return err
	}

	deploymentWarnings := make([]string, 0)

	// Add their declared public SSH keys to the newly created instance
	for _, engagementUser := range engagementUsers {
		_, _, _, _, err = ssh.ParseAuthorizedKey([]byte(engagementUser.SshKey.String))
		if err != nil {
			deploymentWarnings = append(deploymentWarnings, fmt.Sprintf("Invalid user public SSH key, user %s will not be created to the instance", engagementUser.Username))
			logger.Warn("Invalid user public SSH key, user will not be created to the instance", "username", engagementUser.Username, "user_public_ssh_key", engagementUser.SshKey.String, "error", err.Error())
		} else {
			engagementUserID, _ := converters.StringToPgTypeUUID(deployment.UserID)
			userCustomUsername, err := queries.GetUserCustomUsername(context.Background(), *engagementUserID)
			if err != nil {
				logger.Error("Error retrieving custom username for user", "username", engagementUser.Username, "user_public_ssh_key", engagementUser.SshKey.String, "error", err.Error())
				_ = queries.SetDeploymentStatusToError(context.Background(), db.SetDeploymentStatusToErrorParams{
					ErrorMessage: pgtype.Text{String: err.Error(), Valid: true},
					ID:           *deploymentIDPgType,
				})
				return err
			}
			if !userCustomUsername.Valid || len(userCustomUsername.String) == 0 {
				deploymentWarnings = append(deploymentWarnings, fmt.Sprintf("Invalid custom username for user, user '%s' will not be created to the instance", engagementUser.Username))
				logger.Warn("Invalid custom username for user, user will not be created to the instance", "username", engagementUser.Username, "custom_username", engagementUser.CustomUsername.String, "user_public_ssh_key", engagementUser.SshKey.String)
			} else {
				err = sshClient.AddUser(engagementUser.CustomUsername.String, engagementUser.SshKey.String, "fusionx")
				if err != nil {
					deploymentWarnings = append(deploymentWarnings, fmt.Sprintf("Failed to add user '%s' with custom username '%s' to instance: %v", engagementUser.Username, engagementUser.CustomUsername.String, err.Error()))
					logger.Warn("Failed to add user to instance", "username", engagementUser.Username, "custom_username", engagementUser.CustomUsername.String, "user_public_ssh_key", engagementUser.SshKey.String)
				}
				logger.Warn("Successfully added user to instance", "username", engagementUser.Username, "custom_username", engagementUser.CustomUsername.String, "user_public_ssh_key", engagementUser.SshKey.String)
			}
		}
	}

	sshClient.Close()

	// If warnings were found, set deployment to warning
	if len(deploymentWarnings) > 0 {
		err = queries.SetDeploymentStatusToWarning(context.Background(), db.SetDeploymentStatusToWarningParams{
			ID:           *deploymentIDPgType,
			ErrorMessage: pgtype.Text{String: strings.Join(deploymentWarnings, "\n"), Valid: true},
		})
		if err != nil {
			logger.Error("Error updating status of deployment task in the database", "error", err.Error())
			_ = queries.SetDeploymentStatusToError(context.Background(), db.SetDeploymentStatusToErrorParams{
				ErrorMessage: pgtype.Text{String: err.Error(), Valid: true},
				ID:           *deploymentIDPgType,
			})
			return err
		}
		logger.Warn("Deployment completed with warnings")
	} else {
		// No warnings were found, set deployment to successful
		err = queries.UpdateDeploymentStatus(context.Background(), db.UpdateDeploymentStatusParams{
			Status: db.DeploymentStatusEnumSUCCESS,
			ID:     *deploymentIDPgType,
		})
		if err != nil {
			logger.Error("Error updating status of deployment task in the database", "error", err.Error())
			_ = queries.SetDeploymentStatusToError(context.Background(), db.SetDeploymentStatusToErrorParams{
				ErrorMessage: pgtype.Text{String: err.Error(), Valid: true},
				ID:           *deploymentIDPgType,
			})
			return err
		}
	}

	logger.Info("Deployment completed successfully")
	return nil
}
