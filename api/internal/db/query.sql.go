// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: query.sql

package db

import (
	"context"
	"net/netip"

	"github.com/jackc/pgx/v5/pgtype"
)

const addAWSAccount = `-- name: AddAWSAccount :one
INSERT INTO aws_accounts (engagement_id, cloud_account_id, policy_id, status_id, ssh_key_public, error_message, account_creation_status, account_cloud_status, nickname, created_by)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
RETURNING id, engagement_id, cloud_account_id, policy_id, status_id, ssh_key_public, created_at, error_message, account_creation_status, account_cloud_status, nickname, created_by
`

type AddAWSAccountParams struct {
	EngagementID          pgtype.UUID `json:"engagement_id"`
	CloudAccountID        pgtype.Text `json:"cloud_account_id"`
	PolicyID              string      `json:"policy_id"`
	StatusID              string      `json:"status_id"`
	SshKeyPublic          string      `json:"ssh_key_public"`
	ErrorMessage          pgtype.Text `json:"error_message"`
	AccountCreationStatus pgtype.Text `json:"account_creation_status"`
	AccountCloudStatus    pgtype.Text `json:"account_cloud_status"`
	Nickname              string      `json:"nickname"`
	CreatedBy             pgtype.UUID `json:"created_by"`
}

func (q *Queries) AddAWSAccount(ctx context.Context, arg AddAWSAccountParams) (AwsAccount, error) {
	row := q.db.QueryRow(ctx, addAWSAccount,
		arg.EngagementID,
		arg.CloudAccountID,
		arg.PolicyID,
		arg.StatusID,
		arg.SshKeyPublic,
		arg.ErrorMessage,
		arg.AccountCreationStatus,
		arg.AccountCloudStatus,
		arg.Nickname,
		arg.CreatedBy,
	)
	var i AwsAccount
	err := row.Scan(
		&i.ID,
		&i.EngagementID,
		&i.CloudAccountID,
		&i.PolicyID,
		&i.StatusID,
		&i.SshKeyPublic,
		&i.CreatedAt,
		&i.ErrorMessage,
		&i.AccountCreationStatus,
		&i.AccountCloudStatus,
		&i.Nickname,
		&i.CreatedBy,
	)
	return i, err
}

const addAdminUserToEngagement = `-- name: AddAdminUserToEngagement :exec
INSERT INTO engagements_users (user_id, engagement_id)
SELECT $1, $2
WHERE NOT EXISTS (
  SELECT 1
  FROM engagements_users
  WHERE user_id = $1
    AND engagement_id = $2
)
`

type AddAdminUserToEngagementParams struct {
	UserID       pgtype.UUID `json:"user_id"`
	EngagementID pgtype.UUID `json:"engagement_id"`
}

func (q *Queries) AddAdminUserToEngagement(ctx context.Context, arg AddAdminUserToEngagementParams) error {
	_, err := q.db.Exec(ctx, addAdminUserToEngagement, arg.UserID, arg.EngagementID)
	return err
}

const addUsersToEngagement = `-- name: AddUsersToEngagement :exec
WITH new_users AS (SELECT unnest($2::uuid[]) AS user_id)
INSERT
INTO engagements_users (user_id, engagement_id)
SELECT new_users.user_id, $1
FROM new_users
WHERE NOT EXISTS (SELECT 1
                  FROM engagements_users
                  WHERE engagements_users.user_id = new_users.user_id
                    AND engagements_users.engagement_id = $1)
`

type AddUsersToEngagementParams struct {
	EngagementID pgtype.UUID   `json:"engagement_id"`
	Column2      []pgtype.UUID `json:"column_2"`
}

func (q *Queries) AddUsersToEngagement(ctx context.Context, arg AddUsersToEngagementParams) error {
	_, err := q.db.Exec(ctx, addUsersToEngagement, arg.EngagementID, arg.Column2)
	return err
}

const assignUsersToEngagement = `-- name: AssignUsersToEngagement :exec
INSERT INTO engagements_users (engagement_id, user_id)
VALUES ($1, $2)
`

type AssignUsersToEngagementParams struct {
	EngagementID pgtype.UUID `json:"engagement_id"`
	UserID       pgtype.UUID `json:"user_id"`
}

func (q *Queries) AssignUsersToEngagement(ctx context.Context, arg AssignUsersToEngagementParams) error {
	_, err := q.db.Exec(ctx, assignUsersToEngagement, arg.EngagementID, arg.UserID)
	return err
}

const checkNodeGroupId = `-- name: CheckNodeGroupId :one
SELECT CASE
           WHEN n1.node_group_id = n2.node_group_id THEN 'Same'
           ELSE 'Different'
           END AS node_group_comparison
FROM nodes n1
         JOIN
     nodes n2
     ON
         n1.id = $1 AND n2.id = $2
WHERE n1.is_deleted = false
  AND n2.is_deleted = false
`

type CheckNodeGroupIdParams struct {
	ID   pgtype.UUID `json:"id"`
	ID_2 pgtype.UUID `json:"id_2"`
}

func (q *Queries) CheckNodeGroupId(ctx context.Context, arg CheckNodeGroupIdParams) (string, error) {
	row := q.db.QueryRow(ctx, checkNodeGroupId, arg.ID, arg.ID_2)
	var node_group_comparison string
	err := row.Scan(&node_group_comparison)
	return node_group_comparison, err
}

const clearDomainEngagementClientValues = `-- name: ClearDomainEngagementClientValues :exec
UPDATE domains
SET
    engagement = '',
    client = '',
    status = $2
WHERE id = $1
RETURNING id, url, registrar, purchase_date, renewal_date, status, engagement, client, age, created_at
`

type ClearDomainEngagementClientValuesParams struct {
	ID     pgtype.UUID          `json:"id"`
	Status NullDomainStatusEnum `json:"status"`
}

func (q *Queries) ClearDomainEngagementClientValues(ctx context.Context, arg ClearDomainEngagementClientValuesParams) error {
	_, err := q.db.Exec(ctx, clearDomainEngagementClientValues, arg.ID, arg.Status)
	return err
}

const createAdminScript = `-- name: CreateAdminScript :one
INSERT INTO scripts (name, description, content, script_type, user_id, created_at)
VALUES ($1, $2, $3, 'ADMIN', $4, $5)
RETURNING id, name, description, content, script_type, created_at, updated_at, user_id
`

type CreateAdminScriptParams struct {
	Name        string           `json:"name"`
	Description string           `json:"description"`
	Content     string           `json:"content"`
	UserID      pgtype.UUID      `json:"user_id"`
	CreatedAt   pgtype.Timestamp `json:"created_at"`
}

func (q *Queries) CreateAdminScript(ctx context.Context, arg CreateAdminScriptParams) (Script, error) {
	row := q.db.QueryRow(ctx, createAdminScript,
		arg.Name,
		arg.Description,
		arg.Content,
		arg.UserID,
		arg.CreatedAt,
	)
	var i Script
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.Content,
		&i.ScriptType,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.UserID,
	)
	return i, err
}

const createClient = `-- name: CreateClient :exec
INSERT INTO clients (name)
VALUES ($1)
`

func (q *Queries) CreateClient(ctx context.Context, name string) error {
	_, err := q.db.Exec(ctx, createClient, name)
	return err
}

const createCloudInstanceNode = `-- name: CreateCloudInstanceNode :exec
INSERT INTO node_type_cloud_instances (provider, region, operating_system_image_id, instance_type, name, node_id, open_ports, cloud_instance_id, cloud_instance_state, aws_account_id, azure_tenant_id)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
`

type CreateCloudInstanceNodeParams struct {
	Provider               ProviderEnum    `json:"provider"`
	Region                 string          `json:"region"`
	OperatingSystemImageID string          `json:"operating_system_image_id"`
	InstanceType           string          `json:"instance_type"`
	Name                   string          `json:"name"`
	NodeID                 pgtype.UUID     `json:"node_id"`
	OpenPorts              []int32         `json:"open_ports"`
	CloudInstanceID        pgtype.Text     `json:"cloud_instance_id"`
	CloudInstanceState     NullCiStateEnum `json:"cloud_instance_state"`
	AwsAccountID           pgtype.UUID     `json:"aws_account_id"`
	AzureTenantID          pgtype.UUID     `json:"azure_tenant_id"`
}

func (q *Queries) CreateCloudInstanceNode(ctx context.Context, arg CreateCloudInstanceNodeParams) error {
	_, err := q.db.Exec(ctx, createCloudInstanceNode,
		arg.Provider,
		arg.Region,
		arg.OperatingSystemImageID,
		arg.InstanceType,
		arg.Name,
		arg.NodeID,
		arg.OpenPorts,
		arg.CloudInstanceID,
		arg.CloudInstanceState,
		arg.AwsAccountID,
		arg.AzureTenantID,
	)
	return err
}

const createDeployment = `-- name: CreateDeployment :exec
INSERT INTO deployments (id, terraform_module, user_id, status, created_at, node_id, engagement_id)
VALUES ($1, $2, $3, $4, $5, $6, $7)
`

type CreateDeploymentParams struct {
	ID              pgtype.UUID          `json:"id"`
	TerraformModule string               `json:"terraform_module"`
	UserID          pgtype.UUID          `json:"user_id"`
	Status          DeploymentStatusEnum `json:"status"`
	CreatedAt       pgtype.Timestamp     `json:"created_at"`
	NodeID          pgtype.UUID          `json:"node_id"`
	EngagementID    pgtype.UUID          `json:"engagement_id"`
}

func (q *Queries) CreateDeployment(ctx context.Context, arg CreateDeploymentParams) error {
	_, err := q.db.Exec(ctx, createDeployment,
		arg.ID,
		arg.TerraformModule,
		arg.UserID,
		arg.Status,
		arg.CreatedAt,
		arg.NodeID,
		arg.EngagementID,
	)
	return err
}

const createDomain = `-- name: CreateDomain :one
INSERT INTO domains (
    url,
    registrar,
    purchase_date,
    renewal_date,
    status,
    engagement,
    client,
    age
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8
)
RETURNING id, url, registrar, purchase_date, renewal_date, status, engagement, client, age, created_at
`

type CreateDomainParams struct {
	Url          string               `json:"url"`
	Registrar    pgtype.Text          `json:"registrar"`
	PurchaseDate pgtype.Date          `json:"purchase_date"`
	RenewalDate  pgtype.Date          `json:"renewal_date"`
	Status       NullDomainStatusEnum `json:"status"`
	Engagement   pgtype.Text          `json:"engagement"`
	Client       pgtype.Text          `json:"client"`
	Age          pgtype.Int4          `json:"age"`
}

func (q *Queries) CreateDomain(ctx context.Context, arg CreateDomainParams) (Domain, error) {
	row := q.db.QueryRow(ctx, createDomain,
		arg.Url,
		arg.Registrar,
		arg.PurchaseDate,
		arg.RenewalDate,
		arg.Status,
		arg.Engagement,
		arg.Client,
		arg.Age,
	)
	var i Domain
	err := row.Scan(
		&i.ID,
		&i.Url,
		&i.Registrar,
		&i.PurchaseDate,
		&i.RenewalDate,
		&i.Status,
		&i.Engagement,
		&i.Client,
		&i.Age,
		&i.CreatedAt,
	)
	return i, err
}

const createEmailAddressNode = `-- name: CreateEmailAddressNode :exec
INSERT INTO node_type_email_addresses (email_address, node_id)
VALUES ($1, $2)
`

type CreateEmailAddressNodeParams struct {
	EmailAddress string      `json:"email_address"`
	NodeID       pgtype.UUID `json:"node_id"`
}

func (q *Queries) CreateEmailAddressNode(ctx context.Context, arg CreateEmailAddressNodeParams) error {
	_, err := q.db.Exec(ctx, createEmailAddressNode, arg.EmailAddress, arg.NodeID)
	return err
}

const createEngagement = `-- name: CreateEngagement :one
INSERT INTO engagements (title, wbs_code, is_active, client_id)
VALUES ($1, $2, $3, $4)
RETURNING id, title, wbs_code, is_active, client_id, created_at, updated_at, status, error_message
`

type CreateEngagementParams struct {
	Title    string      `json:"title"`
	WbsCode  string      `json:"wbs_code"`
	IsActive bool        `json:"is_active"`
	ClientID pgtype.UUID `json:"client_id"`
}

func (q *Queries) CreateEngagement(ctx context.Context, arg CreateEngagementParams) (Engagement, error) {
	row := q.db.QueryRow(ctx, createEngagement,
		arg.Title,
		arg.WbsCode,
		arg.IsActive,
		arg.ClientID,
	)
	var i Engagement
	err := row.Scan(
		&i.ID,
		&i.Title,
		&i.WbsCode,
		&i.IsActive,
		&i.ClientID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Status,
		&i.ErrorMessage,
	)
	return i, err
}

const createHostNode = `-- name: CreateHostNode :exec
INSERT INTO node_type_hosts (name, ip_addresses, alternative_names, node_id)
VALUES ($1, $2, $3, $4)
`

type CreateHostNodeParams struct {
	Name             string       `json:"name"`
	IpAddresses      []netip.Addr `json:"ip_addresses"`
	AlternativeNames []string     `json:"alternative_names"`
	NodeID           pgtype.UUID  `json:"node_id"`
}

func (q *Queries) CreateHostNode(ctx context.Context, arg CreateHostNodeParams) error {
	_, err := q.db.Exec(ctx, createHostNode,
		arg.Name,
		arg.IpAddresses,
		arg.AlternativeNames,
		arg.NodeID,
	)
	return err
}

const createNode = `-- name: CreateNode :one
INSERT INTO nodes (node_type, name, node_group_id)
VALUES ($1, $2, $3)
RETURNING id, node_type, name, node_group_id, is_deleted
`

type CreateNodeParams struct {
	NodeType    NodeTypeEnum `json:"node_type"`
	Name        string       `json:"name"`
	NodeGroupID pgtype.UUID  `json:"node_group_id"`
}

func (q *Queries) CreateNode(ctx context.Context, arg CreateNodeParams) (Node, error) {
	row := q.db.QueryRow(ctx, createNode, arg.NodeType, arg.Name, arg.NodeGroupID)
	var i Node
	err := row.Scan(
		&i.ID,
		&i.NodeType,
		&i.Name,
		&i.NodeGroupID,
		&i.IsDeleted,
	)
	return i, err
}

const createNodeGroup = `-- name: CreateNodeGroup :one
INSERT INTO node_groups (name, is_active, engagement_id, created_at, updated_at)
VALUES ($1, $2, $3, $4, $5)
RETURNING id, name, is_active, created_at, updated_at, engagement_id
`

type CreateNodeGroupParams struct {
	Name         string           `json:"name"`
	IsActive     bool             `json:"is_active"`
	EngagementID pgtype.UUID      `json:"engagement_id"`
	CreatedAt    pgtype.Timestamp `json:"created_at"`
	UpdatedAt    pgtype.Timestamp `json:"updated_at"`
}

func (q *Queries) CreateNodeGroup(ctx context.Context, arg CreateNodeGroupParams) (NodeGroup, error) {
	row := q.db.QueryRow(ctx, createNodeGroup,
		arg.Name,
		arg.IsActive,
		arg.EngagementID,
		arg.CreatedAt,
		arg.UpdatedAt,
	)
	var i NodeGroup
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.IsActive,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.EngagementID,
	)
	return i, err
}

const createNodeRelationship = `-- name: CreateNodeRelationship :exec
INSERT INTO node_relationships (source_node_id, target_node_id)
VALUES ($1, $2)
`

type CreateNodeRelationshipParams struct {
	SourceNodeID pgtype.UUID `json:"source_node_id"`
	TargetNodeID pgtype.UUID `json:"target_node_id"`
}

func (q *Queries) CreateNodeRelationship(ctx context.Context, arg CreateNodeRelationshipParams) error {
	_, err := q.db.Exec(ctx, createNodeRelationship, arg.SourceNodeID, arg.TargetNodeID)
	return err
}

const createPersonNode = `-- name: CreatePersonNode :exec
INSERT INTO node_type_persons (first_name, last_name, email, company, title, node_id)
VALUES ($1, $2, $3, $4, $5, $6)
`

type CreatePersonNodeParams struct {
	FirstName string      `json:"first_name"`
	LastName  pgtype.Text `json:"last_name"`
	Email     pgtype.Text `json:"email"`
	Company   pgtype.Text `json:"company"`
	Title     pgtype.Text `json:"title"`
	NodeID    pgtype.UUID `json:"node_id"`
}

func (q *Queries) CreatePersonNode(ctx context.Context, arg CreatePersonNodeParams) error {
	_, err := q.db.Exec(ctx, createPersonNode,
		arg.FirstName,
		arg.LastName,
		arg.Email,
		arg.Company,
		arg.Title,
		arg.NodeID,
	)
	return err
}

const createScript = `-- name: CreateScript :one
INSERT INTO scripts (name, description, content, script_type, user_id, created_at)
VALUES ($1, $2, $3, 'STANDARD', $4, $5)
RETURNING id, name, description, content, script_type, created_at, updated_at, user_id
`

type CreateScriptParams struct {
	Name        string           `json:"name"`
	Description string           `json:"description"`
	Content     string           `json:"content"`
	UserID      pgtype.UUID      `json:"user_id"`
	CreatedAt   pgtype.Timestamp `json:"created_at"`
}

func (q *Queries) CreateScript(ctx context.Context, arg CreateScriptParams) (Script, error) {
	row := q.db.QueryRow(ctx, createScript,
		arg.Name,
		arg.Description,
		arg.Content,
		arg.UserID,
		arg.CreatedAt,
	)
	var i Script
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.Content,
		&i.ScriptType,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.UserID,
	)
	return i, err
}

const createUrlNode = `-- name: CreateUrlNode :exec
INSERT INTO node_type_urls (url, node_id)
VALUES ($1, $2)
`

type CreateUrlNodeParams struct {
	Url    string      `json:"url"`
	NodeID pgtype.UUID `json:"node_id"`
}

func (q *Queries) CreateUrlNode(ctx context.Context, arg CreateUrlNodeParams) error {
	_, err := q.db.Exec(ctx, createUrlNode, arg.Url, arg.NodeID)
	return err
}

const createUser = `-- name: CreateUser :exec
INSERT INTO users (id, full_name, username, app_role)
VALUES ($1, $2, $3, $4)
`

type CreateUserParams struct {
	ID       pgtype.UUID `json:"id"`
	FullName pgtype.Text `json:"full_name"`
	Username string      `json:"username"`
	AppRole  pgtype.Text `json:"app_role"`
}

func (q *Queries) CreateUser(ctx context.Context, arg CreateUserParams) error {
	_, err := q.db.Exec(ctx, createUser,
		arg.ID,
		arg.FullName,
		arg.Username,
		arg.AppRole,
	)
	return err
}

const deleteDomain = `-- name: DeleteDomain :exec
DELETE FROM domains
WHERE id = $1
`

func (q *Queries) DeleteDomain(ctx context.Context, id pgtype.UUID) error {
	_, err := q.db.Exec(ctx, deleteDomain, id)
	return err
}

const deleteEngagement = `-- name: DeleteEngagement :exec
UPDATE engagements
SET is_active = false,
    updated_at = NOW()
WHERE id = $1
`

func (q *Queries) DeleteEngagement(ctx context.Context, id pgtype.UUID) error {
	_, err := q.db.Exec(ctx, deleteEngagement, id)
	return err
}

const deleteInstanceType = `-- name: DeleteInstanceType :exec
DELETE FROM instance_size_mappings
WHERE provider = $1
AND size_alias = $2
AND priority = $3
`

type DeleteInstanceTypeParams struct {
	Provider  ProviderEnum `json:"provider"`
	SizeAlias string       `json:"size_alias"`
	Priority  int32        `json:"priority"`
}

func (q *Queries) DeleteInstanceType(ctx context.Context, arg DeleteInstanceTypeParams) error {
	_, err := q.db.Exec(ctx, deleteInstanceType, arg.Provider, arg.SizeAlias, arg.Priority)
	return err
}

const deleteNode = `-- name: DeleteNode :exec
UPDATE nodes
SET is_deleted = true
WHERE id = $1
`

func (q *Queries) DeleteNode(ctx context.Context, id pgtype.UUID) error {
	_, err := q.db.Exec(ctx, deleteNode, id)
	return err
}

const deleteNodeRelationship = `-- name: DeleteNodeRelationship :exec
DELETE
FROM node_relationships
WHERE source_node_id = $1
  AND target_node_id = $2
`

type DeleteNodeRelationshipParams struct {
	SourceNodeID pgtype.UUID `json:"source_node_id"`
	TargetNodeID pgtype.UUID `json:"target_node_id"`
}

func (q *Queries) DeleteNodeRelationship(ctx context.Context, arg DeleteNodeRelationshipParams) error {
	_, err := q.db.Exec(ctx, deleteNodeRelationship, arg.SourceNodeID, arg.TargetNodeID)
	return err
}

const deleteNodeRelationshipsForNode = `-- name: DeleteNodeRelationshipsForNode :exec
DELETE FROM node_relationships
WHERE source_node_id = $1 OR target_node_id = $1
`

func (q *Queries) DeleteNodeRelationshipsForNode(ctx context.Context, sourceNodeID pgtype.UUID) error {
	_, err := q.db.Exec(ctx, deleteNodeRelationshipsForNode, sourceNodeID)
	return err
}

const deleteRelationship = `-- name: DeleteRelationship :exec
DELETE
FROM node_relationships
WHERE (source_node_id, target_node_id) IN (SELECT source_node_id::uuid, target_node_id::uuid
                                           FROM (VALUES ($1, $2)) AS t(source_node_id, target_node_id))
`

type DeleteRelationshipParams struct {
	Column1 interface{} `json:"column_1"`
	Column2 interface{} `json:"column_2"`
}

func (q *Queries) DeleteRelationship(ctx context.Context, arg DeleteRelationshipParams) error {
	_, err := q.db.Exec(ctx, deleteRelationship, arg.Column1, arg.Column2)
	return err
}

const deleteScript = `-- name: DeleteScript :exec
DELETE
FROM scripts
WHERE id = $1
  AND user_id = $2
`

type DeleteScriptParams struct {
	ID     pgtype.UUID `json:"id"`
	UserID pgtype.UUID `json:"user_id"`
}

func (q *Queries) DeleteScript(ctx context.Context, arg DeleteScriptParams) error {
	_, err := q.db.Exec(ctx, deleteScript, arg.ID, arg.UserID)
	return err
}

const deleteUserFromEngagement = `-- name: DeleteUserFromEngagement :exec
DELETE
FROM engagements_users
WHERE engagement_id = $1
`

func (q *Queries) DeleteUserFromEngagement(ctx context.Context, engagementID pgtype.UUID) error {
	_, err := q.db.Exec(ctx, deleteUserFromEngagement, engagementID)
	return err
}

const deleteUsersFromEngagement = `-- name: DeleteUsersFromEngagement :exec
DELETE
FROM engagements_users
WHERE engagement_id = $1
  AND user_id = ANY ($2::uuid[])
`

type DeleteUsersFromEngagementParams struct {
	EngagementID pgtype.UUID   `json:"engagement_id"`
	Column2      []pgtype.UUID `json:"column_2"`
}

func (q *Queries) DeleteUsersFromEngagement(ctx context.Context, arg DeleteUsersFromEngagementParams) error {
	_, err := q.db.Exec(ctx, deleteUsersFromEngagement, arg.EngagementID, arg.Column2)
	return err
}

const editAdminScript = `-- name: EditAdminScript :one
UPDATE scripts
SET name        = $2,
    description = $3,
    content     = $4,
    script_type = 'ADMIN',
    updated_at  = NOW()
WHERE id = $1
  AND user_id = $5
RETURNING id, name, description, content, script_type, created_at, updated_at, user_id
`

type EditAdminScriptParams struct {
	ID          pgtype.UUID `json:"id"`
	Name        string      `json:"name"`
	Description string      `json:"description"`
	Content     string      `json:"content"`
	UserID      pgtype.UUID `json:"user_id"`
}

func (q *Queries) EditAdminScript(ctx context.Context, arg EditAdminScriptParams) (Script, error) {
	row := q.db.QueryRow(ctx, editAdminScript,
		arg.ID,
		arg.Name,
		arg.Description,
		arg.Content,
		arg.UserID,
	)
	var i Script
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.Content,
		&i.ScriptType,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.UserID,
	)
	return i, err
}

const editDomainForAssignment = `-- name: EditDomainForAssignment :exec
UPDATE domains
SET client = $1
WHERE id = $2
`

type EditDomainForAssignmentParams struct {
	Client pgtype.Text `json:"client"`
	ID     pgtype.UUID `json:"id"`
}

func (q *Queries) EditDomainForAssignment(ctx context.Context, arg EditDomainForAssignmentParams) error {
	_, err := q.db.Exec(ctx, editDomainForAssignment, arg.Client, arg.ID)
	return err
}

const editEngagement = `-- name: EditEngagement :exec
UPDATE engagements
SET title=$1,
    wbs_code=$2,
    updated_at=$3
WHERE id = $4
`

type EditEngagementParams struct {
	Title     string           `json:"title"`
	WbsCode   string           `json:"wbs_code"`
	UpdatedAt pgtype.Timestamp `json:"updated_at"`
	ID        pgtype.UUID      `json:"id"`
}

func (q *Queries) EditEngagement(ctx context.Context, arg EditEngagementParams) error {
	_, err := q.db.Exec(ctx, editEngagement,
		arg.Title,
		arg.WbsCode,
		arg.UpdatedAt,
		arg.ID,
	)
	return err
}

const editScript = `-- name: EditScript :one
UPDATE scripts
SET name        = $2,
    description = $3,
    content     = $4,
    script_type = 'STANDARD',
    updated_at  = NOW()
WHERE id = $1
  AND user_id = $5
RETURNING id, name, description, content, script_type, created_at, updated_at, user_id
`

type EditScriptParams struct {
	ID          pgtype.UUID `json:"id"`
	Name        string      `json:"name"`
	Description string      `json:"description"`
	Content     string      `json:"content"`
	UserID      pgtype.UUID `json:"user_id"`
}

func (q *Queries) EditScript(ctx context.Context, arg EditScriptParams) (Script, error) {
	row := q.db.QueryRow(ctx, editScript,
		arg.ID,
		arg.Name,
		arg.Description,
		arg.Content,
		arg.UserID,
	)
	var i Script
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.Content,
		&i.ScriptType,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.UserID,
	)
	return i, err
}

const getAWSAccountsAccountID = `-- name: GetAWSAccountsAccountID :many
SELECT e.id,
       e.account_creation_status,
       e.cloud_account_id,
       e.account_cloud_status
FROM aws_accounts e
WHERE e.cloud_account_id IS NOT NULL
AND e.account_creation_status = 'SUCCESS'
`

type GetAWSAccountsAccountIDRow struct {
	ID                    pgtype.UUID `json:"id"`
	AccountCreationStatus pgtype.Text `json:"account_creation_status"`
	CloudAccountID        pgtype.Text `json:"cloud_account_id"`
	AccountCloudStatus    pgtype.Text `json:"account_cloud_status"`
}

func (q *Queries) GetAWSAccountsAccountID(ctx context.Context) ([]GetAWSAccountsAccountIDRow, error) {
	rows, err := q.db.Query(ctx, getAWSAccountsAccountID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetAWSAccountsAccountIDRow
	for rows.Next() {
		var i GetAWSAccountsAccountIDRow
		if err := rows.Scan(
			&i.ID,
			&i.AccountCreationStatus,
			&i.CloudAccountID,
			&i.AccountCloudStatus,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getAccountSshPrivateKey = `-- name: GetAccountSshPrivateKey :one
SELECT policy_id, status_id
FROM aws_accounts
WHERE aws_accounts.id = $1
`

type GetAccountSshPrivateKeyRow struct {
	PolicyID string `json:"policy_id"`
	StatusID string `json:"status_id"`
}

func (q *Queries) GetAccountSshPrivateKey(ctx context.Context, id pgtype.UUID) (GetAccountSshPrivateKeyRow, error) {
	row := q.db.QueryRow(ctx, getAccountSshPrivateKey, id)
	var i GetAccountSshPrivateKeyRow
	err := row.Scan(&i.PolicyID, &i.StatusID)
	return i, err
}

const getAccountSshPublicKey = `-- name: GetAccountSshPublicKey :one
SELECT ssh_key_public
FROM aws_accounts
WHERE aws_accounts.id = $1
`

func (q *Queries) GetAccountSshPublicKey(ctx context.Context, id pgtype.UUID) (string, error) {
	row := q.db.QueryRow(ctx, getAccountSshPublicKey, id)
	var ssh_key_public string
	err := row.Scan(&ssh_key_public)
	return ssh_key_public, err
}

const getAdminScripts = `-- name: GetAdminScripts :many
SELECT id, name, description, content, script_type, created_at, updated_at, user_id
FROM scripts
WHERE script_type = 'ADMIN'
`

func (q *Queries) GetAdminScripts(ctx context.Context) ([]Script, error) {
	rows, err := q.db.Query(ctx, getAdminScripts)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Script
	for rows.Next() {
		var i Script
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Description,
			&i.Content,
			&i.ScriptType,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.UserID,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getAllAssignmentsLogs = `-- name: GetAllAssignmentsLogs :many
SELECT logs_assignments.id,
       logs_assignments.message,
       logs_assignments.type,
       logs_assignments.status,
       user_id,
       user_custom_username_used,
       node_id,
       nodes.name,
       logs_assignments.created_at
FROM logs_assignments
         JOIN nodes
              ON nodes.id = logs_assignments.node_id
         JOIN node_groups
              ON node_groups.id = nodes.node_group_id
         JOIN engagements
              ON node_groups.engagement_id = engagements.id
`

type GetAllAssignmentsLogsRow struct {
	ID                     pgtype.UUID               `json:"id"`
	Message                string                    `json:"message"`
	Type                   LogsAssignmentsTypeEnum   `json:"type"`
	Status                 LogsAssignmentsStatusEnum `json:"status"`
	UserID                 pgtype.UUID               `json:"user_id"`
	UserCustomUsernameUsed pgtype.Text               `json:"user_custom_username_used"`
	NodeID                 pgtype.UUID               `json:"node_id"`
	Name                   string                    `json:"name"`
	CreatedAt              pgtype.Timestamp          `json:"created_at"`
}

func (q *Queries) GetAllAssignmentsLogs(ctx context.Context) ([]GetAllAssignmentsLogsRow, error) {
	rows, err := q.db.Query(ctx, getAllAssignmentsLogs)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetAllAssignmentsLogsRow
	for rows.Next() {
		var i GetAllAssignmentsLogsRow
		if err := rows.Scan(
			&i.ID,
			&i.Message,
			&i.Type,
			&i.Status,
			&i.UserID,
			&i.UserCustomUsernameUsed,
			&i.NodeID,
			&i.Name,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getAllEngagements = `-- name: GetAllEngagements :many
SELECT id, title, wbs_code, is_active, client_id, created_at, updated_at, status, error_message
FROM engagements
WHERE engagements.is_active = true
ORDER BY engagements.created_at DESC
`

func (q *Queries) GetAllEngagements(ctx context.Context) ([]Engagement, error) {
	rows, err := q.db.Query(ctx, getAllEngagements)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Engagement
	for rows.Next() {
		var i Engagement
		if err := rows.Scan(
			&i.ID,
			&i.Title,
			&i.WbsCode,
			&i.IsActive,
			&i.ClientID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.Status,
			&i.ErrorMessage,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getAllInstanceSizeMappings = `-- name: GetAllInstanceSizeMappings :many
SELECT id, provider, size_alias, priority, instance_type
FROM instance_size_mappings
ORDER BY provider, size_alias, priority
`

func (q *Queries) GetAllInstanceSizeMappings(ctx context.Context) ([]InstanceSizeMapping, error) {
	rows, err := q.db.Query(ctx, getAllInstanceSizeMappings)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []InstanceSizeMapping
	for rows.Next() {
		var i InstanceSizeMapping
		if err := rows.Scan(
			&i.ID,
			&i.Provider,
			&i.SizeAlias,
			&i.Priority,
			&i.InstanceType,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getAllUsers = `-- name: GetAllUsers :many
SELECT id, username, custom_username, full_name, app_role, ssh_key, ssh_key_label, ssh_key_creation_date, is_inactive
FROM users
`

func (q *Queries) GetAllUsers(ctx context.Context) ([]User, error) {
	rows, err := q.db.Query(ctx, getAllUsers)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []User
	for rows.Next() {
		var i User
		if err := rows.Scan(
			&i.ID,
			&i.Username,
			&i.CustomUsername,
			&i.FullName,
			&i.AppRole,
			&i.SshKey,
			&i.SshKeyLabel,
			&i.SshKeyCreationDate,
			&i.IsInactive,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getAssignmentLogs = `-- name: GetAssignmentLogs :many
SELECT logs_assignments.id,
       logs_assignments.message,
       logs_assignments.type,
       logs_assignments.status,
       user_id,
       user_custom_username_used,
       node_id,
       nodes.name,
       logs_assignments.created_at
FROM logs_assignments
         JOIN nodes
              ON nodes.id = logs_assignments.node_id
         JOIN node_groups
              ON node_groups.id = nodes.node_group_id
         JOIN engagements
              ON node_groups.engagement_id = engagements.id
WHERE engagements.id = $1
`

type GetAssignmentLogsRow struct {
	ID                     pgtype.UUID               `json:"id"`
	Message                string                    `json:"message"`
	Type                   LogsAssignmentsTypeEnum   `json:"type"`
	Status                 LogsAssignmentsStatusEnum `json:"status"`
	UserID                 pgtype.UUID               `json:"user_id"`
	UserCustomUsernameUsed pgtype.Text               `json:"user_custom_username_used"`
	NodeID                 pgtype.UUID               `json:"node_id"`
	Name                   string                    `json:"name"`
	CreatedAt              pgtype.Timestamp          `json:"created_at"`
}

func (q *Queries) GetAssignmentLogs(ctx context.Context, id pgtype.UUID) ([]GetAssignmentLogsRow, error) {
	rows, err := q.db.Query(ctx, getAssignmentLogs, id)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetAssignmentLogsRow
	for rows.Next() {
		var i GetAssignmentLogsRow
		if err := rows.Scan(
			&i.ID,
			&i.Message,
			&i.Type,
			&i.Status,
			&i.UserID,
			&i.UserCustomUsernameUsed,
			&i.NodeID,
			&i.Name,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getAzureCloudInstancesWithRegionAndTenantInfo = `-- name: GetAzureCloudInstancesWithRegionAndTenantInfo :many
SELECT ntci.cloud_instance_id, ntci.azure_tenant_id, at.subscription_id
FROM node_type_cloud_instances ntci
JOIN azure_tenants at ON ntci.azure_tenant_id = at.id
WHERE LOWER(ntci.provider::text) = 'azure'
  AND ntci.cloud_instance_id IS NOT NULL
  AND ntci.cloud_instance_state NOT IN ('terminated', 'error')
  AND at.account_cloud_status = 'Enabled'
`

type GetAzureCloudInstancesWithRegionAndTenantInfoRow struct {
	CloudInstanceID pgtype.Text `json:"cloud_instance_id"`
	AzureTenantID   pgtype.UUID `json:"azure_tenant_id"`
	SubscriptionID  pgtype.Text `json:"subscription_id"`
}

func (q *Queries) GetAzureCloudInstancesWithRegionAndTenantInfo(ctx context.Context) ([]GetAzureCloudInstancesWithRegionAndTenantInfoRow, error) {
	rows, err := q.db.Query(ctx, getAzureCloudInstancesWithRegionAndTenantInfo)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetAzureCloudInstancesWithRegionAndTenantInfoRow
	for rows.Next() {
		var i GetAzureCloudInstancesWithRegionAndTenantInfoRow
		if err := rows.Scan(&i.CloudInstanceID, &i.AzureTenantID, &i.SubscriptionID); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getAzureSshPublicKey = `-- name: GetAzureSshPublicKey :one
SELECT ssh_key_public
FROM azure_tenants
WHERE azure_tenants.id = $1
`

func (q *Queries) GetAzureSshPublicKey(ctx context.Context, id pgtype.UUID) (string, error) {
	row := q.db.QueryRow(ctx, getAzureSshPublicKey, id)
	var ssh_key_public string
	err := row.Scan(&ssh_key_public)
	return ssh_key_public, err
}

const getAzureSubscriptionIDById = `-- name: GetAzureSubscriptionIDById :one
SELECT subscription_id
FROM azure_tenants
WHERE id = $1
`

func (q *Queries) GetAzureSubscriptionIDById(ctx context.Context, id pgtype.UUID) (pgtype.Text, error) {
	row := q.db.QueryRow(ctx, getAzureSubscriptionIDById, id)
	var subscription_id pgtype.Text
	err := row.Scan(&subscription_id)
	return subscription_id, err
}

const getAzureTenantBySubscriptionID = `-- name: GetAzureTenantBySubscriptionID :one
SELECT id, engagement_id, tenant_id, subscription_id, error_message, created_at, creation_status, account_cloud_status, policy_id, status_id, ssh_key_public, secrets_saved
FROM azure_tenants
WHERE subscription_id = $1
`

func (q *Queries) GetAzureTenantBySubscriptionID(ctx context.Context, subscriptionID pgtype.Text) (AzureTenant, error) {
	row := q.db.QueryRow(ctx, getAzureTenantBySubscriptionID, subscriptionID)
	var i AzureTenant
	err := row.Scan(
		&i.ID,
		&i.EngagementID,
		&i.TenantID,
		&i.SubscriptionID,
		&i.ErrorMessage,
		&i.CreatedAt,
		&i.CreationStatus,
		&i.AccountCloudStatus,
		&i.PolicyID,
		&i.StatusID,
		&i.SshKeyPublic,
		&i.SecretsSaved,
	)
	return i, err
}

const getAzureTenantByTenantID = `-- name: GetAzureTenantByTenantID :one
SELECT id, engagement_id, tenant_id, subscription_id, error_message, created_at, creation_status, account_cloud_status, policy_id, status_id, ssh_key_public, secrets_saved
FROM azure_tenants
WHERE tenant_id = $1
`

func (q *Queries) GetAzureTenantByTenantID(ctx context.Context, tenantID string) (AzureTenant, error) {
	row := q.db.QueryRow(ctx, getAzureTenantByTenantID, tenantID)
	var i AzureTenant
	err := row.Scan(
		&i.ID,
		&i.EngagementID,
		&i.TenantID,
		&i.SubscriptionID,
		&i.ErrorMessage,
		&i.CreatedAt,
		&i.CreationStatus,
		&i.AccountCloudStatus,
		&i.PolicyID,
		&i.StatusID,
		&i.SshKeyPublic,
		&i.SecretsSaved,
	)
	return i, err
}

const getAzureTenantSshPrivateKey = `-- name: GetAzureTenantSshPrivateKey :one
SELECT policy_id, status_id
FROM azure_tenants
WHERE azure_tenants.id = $1
`

type GetAzureTenantSshPrivateKeyRow struct {
	PolicyID string `json:"policy_id"`
	StatusID string `json:"status_id"`
}

func (q *Queries) GetAzureTenantSshPrivateKey(ctx context.Context, id pgtype.UUID) (GetAzureTenantSshPrivateKeyRow, error) {
	row := q.db.QueryRow(ctx, getAzureTenantSshPrivateKey, id)
	var i GetAzureTenantSshPrivateKeyRow
	err := row.Scan(&i.PolicyID, &i.StatusID)
	return i, err
}

const getClientForName = `-- name: GetClientForName :one
SELECT id, name
FROM clients
WHERE name ILIKE $1
`

func (q *Queries) GetClientForName(ctx context.Context, name string) (Client, error) {
	row := q.db.QueryRow(ctx, getClientForName, name)
	var i Client
	err := row.Scan(&i.ID, &i.Name)
	return i, err
}

const getClients = `-- name: GetClients :many
SELECT name
FROM clients
`

func (q *Queries) GetClients(ctx context.Context) ([]string, error) {
	rows, err := q.db.Query(ctx, getClients)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []string
	for rows.Next() {
		var name string
		if err := rows.Scan(&name); err != nil {
			return nil, err
		}
		items = append(items, name)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getClientsById = `-- name: GetClientsById :one
SELECT name
FROM clients
WHERE id = $1
`

func (q *Queries) GetClientsById(ctx context.Context, id pgtype.UUID) (string, error) {
	row := q.db.QueryRow(ctx, getClientsById, id)
	var name string
	err := row.Scan(&name)
	return name, err
}

const getCloudInstancesWithRegionAndSecretNameForAccount = `-- name: GetCloudInstancesWithRegionAndSecretNameForAccount :many
SELECT
    aws.cloud_account_id,
    ntci.cloud_instance_id,
	  ntci.region,
	  aws.id as secret_id
FROM node_type_cloud_instances ntci
JOIN aws_accounts aws ON ntci.aws_account_id = aws.id
WHERE aws.account_cloud_status = 'ACTIVE'
AND ntci.cloud_instance_state NOT IN ('terminated', 'error')
AND aws.cloud_account_id IS NOT NULL
AND ntci.cloud_instance_id IS NOT NULL
`

type GetCloudInstancesWithRegionAndSecretNameForAccountRow struct {
	CloudAccountID  pgtype.Text `json:"cloud_account_id"`
	CloudInstanceID pgtype.Text `json:"cloud_instance_id"`
	Region          string      `json:"region"`
	SecretID        pgtype.UUID `json:"secret_id"`
}

func (q *Queries) GetCloudInstancesWithRegionAndSecretNameForAccount(ctx context.Context) ([]GetCloudInstancesWithRegionAndSecretNameForAccountRow, error) {
	rows, err := q.db.Query(ctx, getCloudInstancesWithRegionAndSecretNameForAccount)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetCloudInstancesWithRegionAndSecretNameForAccountRow
	for rows.Next() {
		var i GetCloudInstancesWithRegionAndSecretNameForAccountRow
		if err := rows.Scan(
			&i.CloudAccountID,
			&i.CloudInstanceID,
			&i.Region,
			&i.SecretID,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getDeployment = `-- name: GetDeployment :one
SELECT deployments.id, deployments.terraform_module, deployments.status, deployments.created_at, deployments.error_message, deployments.user_id, deployments.node_id, deployments.engagement_id, engagements.title, users.username, nodes.name AS node_name
FROM deployments
         JOIN engagements ON deployments.engagement_id = engagements.id
         JOIN users ON deployments.user_id = users.id
         JOIN nodes ON deployments.node_id = nodes.id
WHERE deployments.id = $1
`

type GetDeploymentRow struct {
	ID              pgtype.UUID          `json:"id"`
	TerraformModule string               `json:"terraform_module"`
	Status          DeploymentStatusEnum `json:"status"`
	CreatedAt       pgtype.Timestamp     `json:"created_at"`
	ErrorMessage    pgtype.Text          `json:"error_message"`
	UserID          pgtype.UUID          `json:"user_id"`
	NodeID          pgtype.UUID          `json:"node_id"`
	EngagementID    pgtype.UUID          `json:"engagement_id"`
	Title           string               `json:"title"`
	Username        string               `json:"username"`
	NodeName        string               `json:"node_name"`
}

func (q *Queries) GetDeployment(ctx context.Context, id pgtype.UUID) (GetDeploymentRow, error) {
	row := q.db.QueryRow(ctx, getDeployment, id)
	var i GetDeploymentRow
	err := row.Scan(
		&i.ID,
		&i.TerraformModule,
		&i.Status,
		&i.CreatedAt,
		&i.ErrorMessage,
		&i.UserID,
		&i.NodeID,
		&i.EngagementID,
		&i.Title,
		&i.Username,
		&i.NodeName,
	)
	return i, err
}

const getDeployments = `-- name: GetDeployments :many
SELECT deployments.id, deployments.terraform_module, deployments.status, deployments.created_at, deployments.error_message, deployments.user_id, deployments.node_id, deployments.engagement_id,
       engagements.title,
       users.username,
       nodes.name AS node_name
FROM deployments
         JOIN engagements ON deployments.engagement_id = engagements.id
         JOIN users ON deployments.user_id = users.id
         JOIN nodes ON deployments.node_id = nodes.id
WHERE deployments.engagement_id = ANY ($1::uuid[])
ORDER BY deployments.created_at DESC
`

type GetDeploymentsRow struct {
	ID              pgtype.UUID          `json:"id"`
	TerraformModule string               `json:"terraform_module"`
	Status          DeploymentStatusEnum `json:"status"`
	CreatedAt       pgtype.Timestamp     `json:"created_at"`
	ErrorMessage    pgtype.Text          `json:"error_message"`
	UserID          pgtype.UUID          `json:"user_id"`
	NodeID          pgtype.UUID          `json:"node_id"`
	EngagementID    pgtype.UUID          `json:"engagement_id"`
	Title           string               `json:"title"`
	Username        string               `json:"username"`
	NodeName        string               `json:"node_name"`
}

func (q *Queries) GetDeployments(ctx context.Context, dollar_1 []pgtype.UUID) ([]GetDeploymentsRow, error) {
	rows, err := q.db.Query(ctx, getDeployments, dollar_1)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetDeploymentsRow
	for rows.Next() {
		var i GetDeploymentsRow
		if err := rows.Scan(
			&i.ID,
			&i.TerraformModule,
			&i.Status,
			&i.CreatedAt,
			&i.ErrorMessage,
			&i.UserID,
			&i.NodeID,
			&i.EngagementID,
			&i.Title,
			&i.Username,
			&i.NodeName,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getDomainByID = `-- name: GetDomainByID :one
SELECT id, url, registrar, purchase_date, renewal_date, status, engagement, client, age, created_at FROM domains
WHERE id = $1
`

func (q *Queries) GetDomainByID(ctx context.Context, id pgtype.UUID) (Domain, error) {
	row := q.db.QueryRow(ctx, getDomainByID, id)
	var i Domain
	err := row.Scan(
		&i.ID,
		&i.Url,
		&i.Registrar,
		&i.PurchaseDate,
		&i.RenewalDate,
		&i.Status,
		&i.Engagement,
		&i.Client,
		&i.Age,
		&i.CreatedAt,
	)
	return i, err
}

const getDomains = `-- name: GetDomains :many
SELECT id, url, registrar, purchase_date, renewal_date, status, engagement, client, age, created_at FROM domains
ORDER BY created_at DESC
`

func (q *Queries) GetDomains(ctx context.Context) ([]Domain, error) {
	rows, err := q.db.Query(ctx, getDomains)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Domain
	for rows.Next() {
		var i Domain
		if err := rows.Scan(
			&i.ID,
			&i.Url,
			&i.Registrar,
			&i.PurchaseDate,
			&i.RenewalDate,
			&i.Status,
			&i.Engagement,
			&i.Client,
			&i.Age,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getDomainsByUrl = `-- name: GetDomainsByUrl :many
SELECT id, url, registrar, purchase_date, renewal_date, status, engagement, client, age, created_at FROM domains
WHERE url = $1
`

func (q *Queries) GetDomainsByUrl(ctx context.Context, url string) ([]Domain, error) {
	rows, err := q.db.Query(ctx, getDomainsByUrl, url)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Domain
	for rows.Next() {
		var i Domain
		if err := rows.Scan(
			&i.ID,
			&i.Url,
			&i.Registrar,
			&i.PurchaseDate,
			&i.RenewalDate,
			&i.Status,
			&i.Engagement,
			&i.Client,
			&i.Age,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getDomainsWithHistory = `-- name: GetDomainsWithHistory :many
SELECT
    ln.message,
    ln.type,
	  ln.created_at,
    u.username AS user_username,
    d.url AS domain_url,
    d.registrar,
    d.engagement,
    d.client,
    d.age
FROM logs_nodes ln
LEFT JOIN users u ON ln.user_id = u.id
LEFT JOIN nodes n ON ln.node_id = n.id
LEFT JOIN node_type_urls ntu ON n.id = ntu.node_id
LEFT JOIN domains d ON LOWER(d.url) = LOWER(ntu.url)
ORDER BY ln.created_at DESC
`

type GetDomainsWithHistoryRow struct {
	Message      string            `json:"message"`
	Type         LogsNodesTypeEnum `json:"type"`
	CreatedAt    pgtype.Timestamp  `json:"created_at"`
	UserUsername pgtype.Text       `json:"user_username"`
	DomainUrl    pgtype.Text       `json:"domain_url"`
	Registrar    pgtype.Text       `json:"registrar"`
	Engagement   pgtype.Text       `json:"engagement"`
	Client       pgtype.Text       `json:"client"`
	Age          pgtype.Int4       `json:"age"`
}

func (q *Queries) GetDomainsWithHistory(ctx context.Context) ([]GetDomainsWithHistoryRow, error) {
	rows, err := q.db.Query(ctx, getDomainsWithHistory)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetDomainsWithHistoryRow
	for rows.Next() {
		var i GetDomainsWithHistoryRow
		if err := rows.Scan(
			&i.Message,
			&i.Type,
			&i.CreatedAt,
			&i.UserUsername,
			&i.DomainUrl,
			&i.Registrar,
			&i.Engagement,
			&i.Client,
			&i.Age,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getEligibleAdminUsers = `-- name: GetEligibleAdminUsers :many
SELECT username
FROM users
WHERE app_role = 'Admin'
  AND custom_username IS NOT NULL
  AND ssh_key IS NOT NULL
`

func (q *Queries) GetEligibleAdminUsers(ctx context.Context) ([]string, error) {
	rows, err := q.db.Query(ctx, getEligibleAdminUsers)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []string
	for rows.Next() {
		var username string
		if err := rows.Scan(&username); err != nil {
			return nil, err
		}
		items = append(items, username)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getEngagement = `-- name: GetEngagement :many
SELECT engagements.id,
       engagements.status,
       engagements.error_message,
       engagements.title      as engagement_title,
       engagements.wbs_code,
       engagements.is_active,
       engagements.created_at as engagement_created_at,
       engagements.updated_at as engagement_updated_at,
       clients.name           as client_name,
       users.id               as user_id,
       users.full_name,
       users.username,
       users.custom_username,
       users.ssh_key
FROM engagements
         JOIN clients on clients.id = engagements.client_id
         LEFT JOIN engagements_users on engagements.id = engagements_users.engagement_id
         LEFT JOIN users on engagements_users.user_id = users.id
WHERE engagements.id = $1
  AND engagements.is_active = true
`

type GetEngagementRow struct {
	ID                  pgtype.UUID          `json:"id"`
	Status              EngagementStatusEnum `json:"status"`
	ErrorMessage        pgtype.Text          `json:"error_message"`
	EngagementTitle     string               `json:"engagement_title"`
	WbsCode             string               `json:"wbs_code"`
	IsActive            bool                 `json:"is_active"`
	EngagementCreatedAt pgtype.Timestamp     `json:"engagement_created_at"`
	EngagementUpdatedAt pgtype.Timestamp     `json:"engagement_updated_at"`
	ClientName          string               `json:"client_name"`
	UserID              pgtype.UUID          `json:"user_id"`
	FullName            pgtype.Text          `json:"full_name"`
	Username            pgtype.Text          `json:"username"`
	CustomUsername      pgtype.Text          `json:"custom_username"`
	SshKey              pgtype.Text          `json:"ssh_key"`
}

func (q *Queries) GetEngagement(ctx context.Context, id pgtype.UUID) ([]GetEngagementRow, error) {
	rows, err := q.db.Query(ctx, getEngagement, id)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetEngagementRow
	for rows.Next() {
		var i GetEngagementRow
		if err := rows.Scan(
			&i.ID,
			&i.Status,
			&i.ErrorMessage,
			&i.EngagementTitle,
			&i.WbsCode,
			&i.IsActive,
			&i.EngagementCreatedAt,
			&i.EngagementUpdatedAt,
			&i.ClientName,
			&i.UserID,
			&i.FullName,
			&i.Username,
			&i.CustomUsername,
			&i.SshKey,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getEngagementAllNodes = `-- name: GetEngagementAllNodes :many
SELECT
    nodes.id            as node_id,
    nodes.node_type,
    nodes.name          as node_name,
    nodes.node_group_id as node_group_id,
    node_groups.name    as node_group_name,
    node_groups.created_at,
    node_groups.updated_at,
    node_type_cloud_instances.cloud_instance_state
FROM nodes
JOIN node_groups ON node_groups.id = nodes.node_group_id
JOIN engagements ON node_groups.engagement_id = engagements.id
JOIN engagements_users ON engagements.id = engagements_users.engagement_id
JOIN users ON engagements_users.user_id = users.id
LEFT JOIN deployments
    ON deployments.node_id = nodes.id
    AND deployments.status IN ('SUCCESS', 'WARNING')
LEFT JOIN node_type_cloud_instances
    ON node_type_cloud_instances.node_id = nodes.id
WHERE engagements.id = $1
  AND users.id = $2
  AND nodes.is_deleted = false
`

type GetEngagementAllNodesParams struct {
	ID   pgtype.UUID `json:"id"`
	ID_2 pgtype.UUID `json:"id_2"`
}

type GetEngagementAllNodesRow struct {
	NodeID             pgtype.UUID      `json:"node_id"`
	NodeType           NodeTypeEnum     `json:"node_type"`
	NodeName           string           `json:"node_name"`
	NodeGroupID        pgtype.UUID      `json:"node_group_id"`
	NodeGroupName      string           `json:"node_group_name"`
	CreatedAt          pgtype.Timestamp `json:"created_at"`
	UpdatedAt          pgtype.Timestamp `json:"updated_at"`
	CloudInstanceState NullCiStateEnum  `json:"cloud_instance_state"`
}

func (q *Queries) GetEngagementAllNodes(ctx context.Context, arg GetEngagementAllNodesParams) ([]GetEngagementAllNodesRow, error) {
	rows, err := q.db.Query(ctx, getEngagementAllNodes, arg.ID, arg.ID_2)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetEngagementAllNodesRow
	for rows.Next() {
		var i GetEngagementAllNodesRow
		if err := rows.Scan(
			&i.NodeID,
			&i.NodeType,
			&i.NodeName,
			&i.NodeGroupID,
			&i.NodeGroupName,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.CloudInstanceState,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getEngagementByTitle = `-- name: GetEngagementByTitle :many
SELECT engagements.id,
       engagements.status,
       engagements.error_message,
       engagements.title      as engagement_title,
       engagements.wbs_code,
       engagements.is_active,
       engagements.created_at as engagement_created_at,
       engagements.updated_at as engagement_updated_at,
       clients.name           as client_name,
       users.id               as user_id,
       users.full_name,
       users.username,
       users.custom_username,
       users.ssh_key
FROM engagements
         JOIN clients on clients.id = engagements.client_id
         LEFT JOIN engagements_users on engagements.id = engagements_users.engagement_id
         LEFT JOIN users on engagements_users.user_id = users.id
WHERE engagements.title = $1
  AND engagements.is_active = true
`

type GetEngagementByTitleRow struct {
	ID                  pgtype.UUID          `json:"id"`
	Status              EngagementStatusEnum `json:"status"`
	ErrorMessage        pgtype.Text          `json:"error_message"`
	EngagementTitle     string               `json:"engagement_title"`
	WbsCode             string               `json:"wbs_code"`
	IsActive            bool                 `json:"is_active"`
	EngagementCreatedAt pgtype.Timestamp     `json:"engagement_created_at"`
	EngagementUpdatedAt pgtype.Timestamp     `json:"engagement_updated_at"`
	ClientName          string               `json:"client_name"`
	UserID              pgtype.UUID          `json:"user_id"`
	FullName            pgtype.Text          `json:"full_name"`
	Username            pgtype.Text          `json:"username"`
	CustomUsername      pgtype.Text          `json:"custom_username"`
	SshKey              pgtype.Text          `json:"ssh_key"`
}

func (q *Queries) GetEngagementByTitle(ctx context.Context, title string) ([]GetEngagementByTitleRow, error) {
	rows, err := q.db.Query(ctx, getEngagementByTitle, title)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetEngagementByTitleRow
	for rows.Next() {
		var i GetEngagementByTitleRow
		if err := rows.Scan(
			&i.ID,
			&i.Status,
			&i.ErrorMessage,
			&i.EngagementTitle,
			&i.WbsCode,
			&i.IsActive,
			&i.EngagementCreatedAt,
			&i.EngagementUpdatedAt,
			&i.ClientName,
			&i.UserID,
			&i.FullName,
			&i.Username,
			&i.CustomUsername,
			&i.SshKey,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getEngagementCloudInstances = `-- name: GetEngagementCloudInstances :many
SELECT provider, region, operating_system_image_id, instance_type, node_type_cloud_instances.name, open_ports, public_ipv4_address, node_type_cloud_instances.node_id, cloud_instance_state, cloud_instance_id, aws_account_id, azure_tenant_id, n.id, node_type, n.name, node_group_id, is_deleted, ng.id, ng.name, ng.is_active, ng.created_at, ng.updated_at, engagement_id, e.id, title, wbs_code, e.is_active, client_id, e.created_at, e.updated_at, e.status, error_message, d.node_id, d.status,
       d.status AS CI_deployment_status
FROM node_type_cloud_instances
         JOIN nodes n ON node_type_cloud_instances.node_id = n.id
         JOIN node_groups ng ON n.node_group_id = ng.id
         JOIN engagements e ON ng.engagement_id = e.id
         LEFT JOIN (
              SELECT DISTINCT ON (deployments.node_id)
                      deployments.node_id,
                      deployments.status
              FROM deployments
              ORDER BY deployments.node_id, deployments.created_at DESC
         ) d ON d.node_id = node_type_cloud_instances.node_id
WHERE e.id = $1
  AND n.is_deleted = false
`

type GetEngagementCloudInstancesRow struct {
	Provider               ProviderEnum         `json:"provider"`
	Region                 string               `json:"region"`
	OperatingSystemImageID string               `json:"operating_system_image_id"`
	InstanceType           string               `json:"instance_type"`
	Name                   string               `json:"name"`
	OpenPorts              []int32              `json:"open_ports"`
	PublicIpv4Address      *netip.Addr          `json:"public_ipv4_address"`
	NodeID                 pgtype.UUID          `json:"node_id"`
	CloudInstanceState     NullCiStateEnum      `json:"cloud_instance_state"`
	CloudInstanceID        pgtype.Text          `json:"cloud_instance_id"`
	AwsAccountID           pgtype.UUID          `json:"aws_account_id"`
	AzureTenantID          pgtype.UUID          `json:"azure_tenant_id"`
	ID                     pgtype.UUID          `json:"id"`
	NodeType               NodeTypeEnum         `json:"node_type"`
	Name_2                 string               `json:"name_2"`
	NodeGroupID            pgtype.UUID          `json:"node_group_id"`
	IsDeleted              bool                 `json:"is_deleted"`
	ID_2                   pgtype.UUID          `json:"id_2"`
	Name_3                 string               `json:"name_3"`
	IsActive               bool                 `json:"is_active"`
	CreatedAt              pgtype.Timestamp     `json:"created_at"`
	UpdatedAt              pgtype.Timestamp     `json:"updated_at"`
	EngagementID           pgtype.UUID          `json:"engagement_id"`
	ID_3                   pgtype.UUID          `json:"id_3"`
	Title                  string               `json:"title"`
	WbsCode                string               `json:"wbs_code"`
	IsActive_2             bool                 `json:"is_active_2"`
	ClientID               pgtype.UUID          `json:"client_id"`
	CreatedAt_2            pgtype.Timestamp     `json:"created_at_2"`
	UpdatedAt_2            pgtype.Timestamp     `json:"updated_at_2"`
	Status                 EngagementStatusEnum `json:"status"`
	ErrorMessage           pgtype.Text          `json:"error_message"`
	NodeID_2               pgtype.UUID          `json:"node_id_2"`
	Status_2               DeploymentStatusEnum `json:"status_2"`
	CiDeploymentStatus     DeploymentStatusEnum `json:"ci_deployment_status"`
}

func (q *Queries) GetEngagementCloudInstances(ctx context.Context, id pgtype.UUID) ([]GetEngagementCloudInstancesRow, error) {
	rows, err := q.db.Query(ctx, getEngagementCloudInstances, id)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetEngagementCloudInstancesRow
	for rows.Next() {
		var i GetEngagementCloudInstancesRow
		if err := rows.Scan(
			&i.Provider,
			&i.Region,
			&i.OperatingSystemImageID,
			&i.InstanceType,
			&i.Name,
			&i.OpenPorts,
			&i.PublicIpv4Address,
			&i.NodeID,
			&i.CloudInstanceState,
			&i.CloudInstanceID,
			&i.AwsAccountID,
			&i.AzureTenantID,
			&i.ID,
			&i.NodeType,
			&i.Name_2,
			&i.NodeGroupID,
			&i.IsDeleted,
			&i.ID_2,
			&i.Name_3,
			&i.IsActive,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.EngagementID,
			&i.ID_3,
			&i.Title,
			&i.WbsCode,
			&i.IsActive_2,
			&i.ClientID,
			&i.CreatedAt_2,
			&i.UpdatedAt_2,
			&i.Status,
			&i.ErrorMessage,
			&i.NodeID_2,
			&i.Status_2,
			&i.CiDeploymentStatus,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getEngagementCloudInstancesForUser = `-- name: GetEngagementCloudInstancesForUser :many
SELECT n.id                                  as node_id,
       ng.id                                 as node_group_id,
       ng.name                               as node_group_name,
       ng.is_active                          as node_group_is_active,
       ng.created_at                         as node_group_created_at,
       ng.updated_at                         as node_group_updated_at,
       engagements.title,
       node_type_cloud_instances.operating_system_image_id,
       node_type_cloud_instances.provider,
       node_type_cloud_instances.name,
       node_type_cloud_instances.region,
       node_type_cloud_instances.public_ipv4_address,
       node_type_cloud_instances.open_ports,
       clients.name                          as client_name,
       MIN(logs_nodes.created_at)::TIMESTAMP AS node_created_at,
       MAX(logs_nodes.created_at)::TIMESTAMP AS node_updated_at,
       COALESCE(d.status, 'ERROR')          AS CI_deployment_status,
       cloud_instance_state,
       cloud_instance_id
FROM node_type_cloud_instances
         JOIN nodes n on n.id = node_type_cloud_instances.node_id
         JOIN node_groups ng on n.node_group_id = ng.id
         JOIN engagements on ng.engagement_id = engagements.id
         JOIN engagements_users on engagements.id = engagements_users.engagement_id
         JOIN users on engagements_users.user_id = users.id
         JOIN clients on engagements.client_id = clients.id
         LEFT JOIN logs_nodes on n.id = logs_nodes.node_id
         LEFT JOIN (
             SELECT DISTINCT ON (deployments.node_id)
                    deployments.node_id,
                    deployments.status
             FROM deployments
             ORDER BY deployments.node_id, deployments.created_at DESC
         ) d ON d.node_id = n.id
WHERE engagements.id = ANY ($2::uuid[])
  AND users.id = $1
  AND n.is_deleted = false
GROUP BY n.id, ng.id, node_type_cloud_instances.operating_system_image_id, engagements.title,
         node_type_cloud_instances.provider, node_type_cloud_instances.name,
         node_type_cloud_instances.region, node_type_cloud_instances.public_ipv4_address,
         node_type_cloud_instances.open_ports,
         clients.name, d.status, cloud_instance_state, cloud_instance_id
ORDER BY node_updated_at DESC
`

type GetEngagementCloudInstancesForUserParams struct {
	ID  pgtype.UUID   `json:"id"`
	Ids []pgtype.UUID `json:"ids"`
}

type GetEngagementCloudInstancesForUserRow struct {
	NodeID                 pgtype.UUID          `json:"node_id"`
	NodeGroupID            pgtype.UUID          `json:"node_group_id"`
	NodeGroupName          string               `json:"node_group_name"`
	NodeGroupIsActive      bool                 `json:"node_group_is_active"`
	NodeGroupCreatedAt     pgtype.Timestamp     `json:"node_group_created_at"`
	NodeGroupUpdatedAt     pgtype.Timestamp     `json:"node_group_updated_at"`
	Title                  string               `json:"title"`
	OperatingSystemImageID string               `json:"operating_system_image_id"`
	Provider               ProviderEnum         `json:"provider"`
	Name                   string               `json:"name"`
	Region                 string               `json:"region"`
	PublicIpv4Address      *netip.Addr          `json:"public_ipv4_address"`
	OpenPorts              []int32              `json:"open_ports"`
	ClientName             string               `json:"client_name"`
	NodeCreatedAt          pgtype.Timestamp     `json:"node_created_at"`
	NodeUpdatedAt          pgtype.Timestamp     `json:"node_updated_at"`
	CiDeploymentStatus     DeploymentStatusEnum `json:"ci_deployment_status"`
	CloudInstanceState     NullCiStateEnum      `json:"cloud_instance_state"`
	CloudInstanceID        pgtype.Text          `json:"cloud_instance_id"`
}

func (q *Queries) GetEngagementCloudInstancesForUser(ctx context.Context, arg GetEngagementCloudInstancesForUserParams) ([]GetEngagementCloudInstancesForUserRow, error) {
	rows, err := q.db.Query(ctx, getEngagementCloudInstancesForUser, arg.ID, arg.Ids)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetEngagementCloudInstancesForUserRow
	for rows.Next() {
		var i GetEngagementCloudInstancesForUserRow
		if err := rows.Scan(
			&i.NodeID,
			&i.NodeGroupID,
			&i.NodeGroupName,
			&i.NodeGroupIsActive,
			&i.NodeGroupCreatedAt,
			&i.NodeGroupUpdatedAt,
			&i.Title,
			&i.OperatingSystemImageID,
			&i.Provider,
			&i.Name,
			&i.Region,
			&i.PublicIpv4Address,
			&i.OpenPorts,
			&i.ClientName,
			&i.NodeCreatedAt,
			&i.NodeUpdatedAt,
			&i.CiDeploymentStatus,
			&i.CloudInstanceState,
			&i.CloudInstanceID,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getEngagementEmailAddresses = `-- name: GetEngagementEmailAddresses :many
SELECT n.id                                  as node_id,
       ng.id                                 as node_group_id,
       ng.name                               as node_group_name,
       ng.is_active                          as node_group_is_active,
       ng.created_at                         as node_group_created_at,
       ng.updated_at                         as node_group_updated_at,
       engagements.title,
       node_type_email_addresses.email_address,
       clients.name                          as client_name,
       MIN(logs_nodes.created_at)::TIMESTAMP AS node_created_at,
       MAX(logs_nodes.created_at)::TIMESTAMP AS node_updated_at
FROM node_type_email_addresses
         JOIN nodes n on n.id = node_type_email_addresses.node_id
         JOIN node_groups ng on n.node_group_id = ng.id
         JOIN engagements on ng.engagement_id = engagements.id
         JOIN engagements_users on engagements.id = engagements_users.engagement_id
         JOIN users on engagements_users.user_id = users.id
         JOIN clients on engagements.client_id = clients.id
         LEFT JOIN logs_nodes on n.id = logs_nodes.node_id
WHERE engagements.id = ANY ($2::uuid[])
  AND users.id = $1
  AND n.is_deleted = false
GROUP BY n.id, ng.id, engagements.title, node_type_email_addresses.email_address, node_type_email_addresses.node_id,
         clients.name
ORDER BY node_updated_at DESC
`

type GetEngagementEmailAddressesParams struct {
	ID  pgtype.UUID   `json:"id"`
	Ids []pgtype.UUID `json:"ids"`
}

type GetEngagementEmailAddressesRow struct {
	NodeID             pgtype.UUID      `json:"node_id"`
	NodeGroupID        pgtype.UUID      `json:"node_group_id"`
	NodeGroupName      string           `json:"node_group_name"`
	NodeGroupIsActive  bool             `json:"node_group_is_active"`
	NodeGroupCreatedAt pgtype.Timestamp `json:"node_group_created_at"`
	NodeGroupUpdatedAt pgtype.Timestamp `json:"node_group_updated_at"`
	Title              string           `json:"title"`
	EmailAddress       string           `json:"email_address"`
	ClientName         string           `json:"client_name"`
	NodeCreatedAt      pgtype.Timestamp `json:"node_created_at"`
	NodeUpdatedAt      pgtype.Timestamp `json:"node_updated_at"`
}

func (q *Queries) GetEngagementEmailAddresses(ctx context.Context, arg GetEngagementEmailAddressesParams) ([]GetEngagementEmailAddressesRow, error) {
	rows, err := q.db.Query(ctx, getEngagementEmailAddresses, arg.ID, arg.Ids)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetEngagementEmailAddressesRow
	for rows.Next() {
		var i GetEngagementEmailAddressesRow
		if err := rows.Scan(
			&i.NodeID,
			&i.NodeGroupID,
			&i.NodeGroupName,
			&i.NodeGroupIsActive,
			&i.NodeGroupCreatedAt,
			&i.NodeGroupUpdatedAt,
			&i.Title,
			&i.EmailAddress,
			&i.ClientName,
			&i.NodeCreatedAt,
			&i.NodeUpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getEngagementGraphRelationships = `-- name: GetEngagementGraphRelationships :many
SELECT node_relationships.source_node_id,
       node_relationships.target_node_id,
       node_groups.id as node_group_id
FROM node_relationships
         JOIN nodes on node_relationships.source_node_id = nodes.id
         JOIN node_groups on node_groups.id = nodes.node_group_id
         JOIN engagements on node_groups.engagement_id = engagements.id
         JOIN engagements_users on engagements.id = engagements_users.engagement_id
         JOIN users on engagements_users.user_id = users.id
WHERE engagements.id = $1
  AND users.id = $2
  AND is_deleted = false
`

type GetEngagementGraphRelationshipsParams struct {
	ID   pgtype.UUID `json:"id"`
	ID_2 pgtype.UUID `json:"id_2"`
}

type GetEngagementGraphRelationshipsRow struct {
	SourceNodeID pgtype.UUID `json:"source_node_id"`
	TargetNodeID pgtype.UUID `json:"target_node_id"`
	NodeGroupID  pgtype.UUID `json:"node_group_id"`
}

func (q *Queries) GetEngagementGraphRelationships(ctx context.Context, arg GetEngagementGraphRelationshipsParams) ([]GetEngagementGraphRelationshipsRow, error) {
	rows, err := q.db.Query(ctx, getEngagementGraphRelationships, arg.ID, arg.ID_2)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetEngagementGraphRelationshipsRow
	for rows.Next() {
		var i GetEngagementGraphRelationshipsRow
		if err := rows.Scan(&i.SourceNodeID, &i.TargetNodeID, &i.NodeGroupID); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getEngagementHosts = `-- name: GetEngagementHosts :many
SELECT n.id                                  as node_id,
       ng.id                                 as node_group_id,
       ng.name                               as node_group_name,
       ng.is_active                          as node_group_is_active,
       ng.created_at                         as node_group_created_at,
       ng.updated_at                         as node_group_updated_at,
       engagements.title,
       node_type_hosts.ip_addresses,
       node_type_hosts.alternative_names,
       node_type_hosts.name,
       clients.name                          as client_name,
       MIN(logs_nodes.created_at)::TIMESTAMP AS node_created_at,
       MAX(logs_nodes.created_at)::TIMESTAMP AS node_updated_at
FROM node_type_hosts
         JOIN nodes n on n.id = node_type_hosts.node_id
         JOIN node_groups ng on n.node_group_id = ng.id
         JOIN engagements on ng.engagement_id = engagements.id
         JOIN engagements_users on engagements.id = engagements_users.engagement_id
         JOIN users on engagements_users.user_id = users.id
         JOIN clients on engagements.client_id = clients.id
         LEFT JOIN logs_nodes on n.id = logs_nodes.node_id

WHERE engagements.id = ANY ($2::uuid[])
  AND users.id = $1
  AND n.is_deleted = false
GROUP BY n.id, ng.id, engagements.title, node_type_hosts.ip_addresses, node_type_hosts.alternative_names,
         node_type_hosts.name, node_type_hosts.node_id, clients.name
ORDER BY node_updated_at DESC
`

type GetEngagementHostsParams struct {
	ID  pgtype.UUID   `json:"id"`
	Ids []pgtype.UUID `json:"ids"`
}

type GetEngagementHostsRow struct {
	NodeID             pgtype.UUID      `json:"node_id"`
	NodeGroupID        pgtype.UUID      `json:"node_group_id"`
	NodeGroupName      string           `json:"node_group_name"`
	NodeGroupIsActive  bool             `json:"node_group_is_active"`
	NodeGroupCreatedAt pgtype.Timestamp `json:"node_group_created_at"`
	NodeGroupUpdatedAt pgtype.Timestamp `json:"node_group_updated_at"`
	Title              string           `json:"title"`
	IpAddresses        []netip.Addr     `json:"ip_addresses"`
	AlternativeNames   []string         `json:"alternative_names"`
	Name               string           `json:"name"`
	ClientName         string           `json:"client_name"`
	NodeCreatedAt      pgtype.Timestamp `json:"node_created_at"`
	NodeUpdatedAt      pgtype.Timestamp `json:"node_updated_at"`
}

func (q *Queries) GetEngagementHosts(ctx context.Context, arg GetEngagementHostsParams) ([]GetEngagementHostsRow, error) {
	rows, err := q.db.Query(ctx, getEngagementHosts, arg.ID, arg.Ids)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetEngagementHostsRow
	for rows.Next() {
		var i GetEngagementHostsRow
		if err := rows.Scan(
			&i.NodeID,
			&i.NodeGroupID,
			&i.NodeGroupName,
			&i.NodeGroupIsActive,
			&i.NodeGroupCreatedAt,
			&i.NodeGroupUpdatedAt,
			&i.Title,
			&i.IpAddresses,
			&i.AlternativeNames,
			&i.Name,
			&i.ClientName,
			&i.NodeCreatedAt,
			&i.NodeUpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getEngagementNodeGroup = `-- name: GetEngagementNodeGroup :many
SELECT id, name, is_active, created_at, updated_at, engagement_id
FROM node_groups
WHERE node_groups.id = $1
  AND engagement_id = $2
`

type GetEngagementNodeGroupParams struct {
	ID           pgtype.UUID `json:"id"`
	EngagementID pgtype.UUID `json:"engagement_id"`
}

func (q *Queries) GetEngagementNodeGroup(ctx context.Context, arg GetEngagementNodeGroupParams) ([]NodeGroup, error) {
	rows, err := q.db.Query(ctx, getEngagementNodeGroup, arg.ID, arg.EngagementID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []NodeGroup
	for rows.Next() {
		var i NodeGroup
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.IsActive,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.EngagementID,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getEngagementNodeGroupGraphRelationships = `-- name: GetEngagementNodeGroupGraphRelationships :many
SELECT node_relationships.source_node_id,
       node_relationships.target_node_id,
       node_groups.id as node_group_id
FROM node_relationships
         JOIN public.nodes on node_relationships.source_node_id = nodes.id
         JOIN public.node_groups on node_groups.id = nodes.node_group_id
         JOIN public.engagements on node_groups.engagement_id = engagements.id
WHERE engagements.id = $1
  AND nodes.node_group_id = $2
  AND nodes.is_deleted = false
`

type GetEngagementNodeGroupGraphRelationshipsParams struct {
	ID          pgtype.UUID `json:"id"`
	NodeGroupID pgtype.UUID `json:"node_group_id"`
}

type GetEngagementNodeGroupGraphRelationshipsRow struct {
	SourceNodeID pgtype.UUID `json:"source_node_id"`
	TargetNodeID pgtype.UUID `json:"target_node_id"`
	NodeGroupID  pgtype.UUID `json:"node_group_id"`
}

func (q *Queries) GetEngagementNodeGroupGraphRelationships(ctx context.Context, arg GetEngagementNodeGroupGraphRelationshipsParams) ([]GetEngagementNodeGroupGraphRelationshipsRow, error) {
	rows, err := q.db.Query(ctx, getEngagementNodeGroupGraphRelationships, arg.ID, arg.NodeGroupID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetEngagementNodeGroupGraphRelationshipsRow
	for rows.Next() {
		var i GetEngagementNodeGroupGraphRelationshipsRow
		if err := rows.Scan(&i.SourceNodeID, &i.TargetNodeID, &i.NodeGroupID); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getEngagementNodeGroupNodes = `-- name: GetEngagementNodeGroupNodes :many
SELECT nodes.id            as node_id,
       nodes.node_type,
       nodes.name          as node_name,
       nodes.node_group_id as node_group_id,
       node_groups.updated_at,
       node_groups.created_at
FROM nodes
         JOIN node_groups on node_groups.id = nodes.node_group_id
         JOIN engagements on node_groups.engagement_id = engagements.id
         JOIN engagements_users on engagements.id = engagements_users.engagement_id
         JOIN users on engagements_users.user_id = users.id
WHERE engagements.id = $1
  AND users.id = $2
  AND nodes.is_deleted = false
  AND nodes.node_group_id = $3
`

type GetEngagementNodeGroupNodesParams struct {
	ID          pgtype.UUID `json:"id"`
	ID_2        pgtype.UUID `json:"id_2"`
	NodeGroupID pgtype.UUID `json:"node_group_id"`
}

type GetEngagementNodeGroupNodesRow struct {
	NodeID      pgtype.UUID      `json:"node_id"`
	NodeType    NodeTypeEnum     `json:"node_type"`
	NodeName    string           `json:"node_name"`
	NodeGroupID pgtype.UUID      `json:"node_group_id"`
	UpdatedAt   pgtype.Timestamp `json:"updated_at"`
	CreatedAt   pgtype.Timestamp `json:"created_at"`
}

func (q *Queries) GetEngagementNodeGroupNodes(ctx context.Context, arg GetEngagementNodeGroupNodesParams) ([]GetEngagementNodeGroupNodesRow, error) {
	rows, err := q.db.Query(ctx, getEngagementNodeGroupNodes, arg.ID, arg.ID_2, arg.NodeGroupID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetEngagementNodeGroupNodesRow
	for rows.Next() {
		var i GetEngagementNodeGroupNodesRow
		if err := rows.Scan(
			&i.NodeID,
			&i.NodeType,
			&i.NodeName,
			&i.NodeGroupID,
			&i.UpdatedAt,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getEngagementNodesGroups = `-- name: GetEngagementNodesGroups :many
SELECT nodes.id                              as node_id,
       nodes.node_type,
       nodes.name                            as node_name,
       engagements.title                     as engagement_name,
       engagements.is_active,
       nodes.node_group_id                   as node_group_id,
       MAX(logs_nodes.created_at)::TIMESTAMP AS node_updated_at,
       clients.name                          as client_name
FROM nodes
         JOIN node_groups on node_groups.id = nodes.node_group_id
         JOIN engagements on node_groups.engagement_id = engagements.id
         JOIN clients on clients.id = engagements.client_id
         JOIN engagements_users on engagements.id = engagements_users.engagement_id
         JOIN users on engagements_users.user_id = users.id
         LEFT JOIN logs_nodes ON logs_nodes.node_id = nodes.id
WHERE users.id = $1
  AND nodes.is_deleted = false
GROUP BY nodes.id, nodes.node_type, nodes.name, engagements.title, engagements.is_active,
         nodes.node_group_id, clients.name
ORDER BY node_updated_at DESC
`

type GetEngagementNodesGroupsRow struct {
	NodeID         pgtype.UUID      `json:"node_id"`
	NodeType       NodeTypeEnum     `json:"node_type"`
	NodeName       string           `json:"node_name"`
	EngagementName string           `json:"engagement_name"`
	IsActive       bool             `json:"is_active"`
	NodeGroupID    pgtype.UUID      `json:"node_group_id"`
	NodeUpdatedAt  pgtype.Timestamp `json:"node_updated_at"`
	ClientName     string           `json:"client_name"`
}

func (q *Queries) GetEngagementNodesGroups(ctx context.Context, id pgtype.UUID) ([]GetEngagementNodesGroupsRow, error) {
	rows, err := q.db.Query(ctx, getEngagementNodesGroups, id)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetEngagementNodesGroupsRow
	for rows.Next() {
		var i GetEngagementNodesGroupsRow
		if err := rows.Scan(
			&i.NodeID,
			&i.NodeType,
			&i.NodeName,
			&i.EngagementName,
			&i.IsActive,
			&i.NodeGroupID,
			&i.NodeUpdatedAt,
			&i.ClientName,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getEngagementPersons = `-- name: GetEngagementPersons :many
SELECT n.id                                  as node_id,
       ng.id                                 as node_group_id,
       ng.name                               as node_group_name,
       ng.is_active                          as node_group_is_active,
       ng.created_at                         as node_group_created_at,
       ng.updated_at                         as node_group_updated_at,
       engagements.title,
       node_type_persons.first_name,
       node_type_persons.last_name,
       node_type_persons.email,
       node_type_persons.company,
       node_type_persons.title,
       clients.name                          as client_name,
       MIN(logs_nodes.created_at)::TIMESTAMP AS node_created_at,
       MAX(logs_nodes.created_at)::TIMESTAMP AS node_updated_at
FROM node_type_persons
         JOIN nodes n on n.id = node_type_persons.node_id
         JOIN node_groups ng on n.node_group_id = ng.id
         JOIN engagements on ng.engagement_id = engagements.id
         JOIN engagements_users on engagements.id = engagements_users.engagement_id
         JOIN users on engagements_users.user_id = users.id
         JOIN clients on engagements.client_id = clients.id
         LEFT JOIN logs_nodes on n.id = logs_nodes.node_id
WHERE engagements.id = ANY ($2::uuid[])
  AND users.id = $1
  AND n.is_deleted = false
GROUP BY n.id, ng.id, engagements.title, node_type_persons.first_name, node_type_persons.last_name,
         node_type_persons.email, node_type_persons.company, node_type_persons.title, node_type_persons.node_id,
         clients.name
ORDER BY node_updated_at DESC
`

type GetEngagementPersonsParams struct {
	ID  pgtype.UUID   `json:"id"`
	Ids []pgtype.UUID `json:"ids"`
}

type GetEngagementPersonsRow struct {
	NodeID             pgtype.UUID      `json:"node_id"`
	NodeGroupID        pgtype.UUID      `json:"node_group_id"`
	NodeGroupName      string           `json:"node_group_name"`
	NodeGroupIsActive  bool             `json:"node_group_is_active"`
	NodeGroupCreatedAt pgtype.Timestamp `json:"node_group_created_at"`
	NodeGroupUpdatedAt pgtype.Timestamp `json:"node_group_updated_at"`
	Title              string           `json:"title"`
	FirstName          string           `json:"first_name"`
	LastName           pgtype.Text      `json:"last_name"`
	Email              pgtype.Text      `json:"email"`
	Company            pgtype.Text      `json:"company"`
	Title_2            pgtype.Text      `json:"title_2"`
	ClientName         string           `json:"client_name"`
	NodeCreatedAt      pgtype.Timestamp `json:"node_created_at"`
	NodeUpdatedAt      pgtype.Timestamp `json:"node_updated_at"`
}

func (q *Queries) GetEngagementPersons(ctx context.Context, arg GetEngagementPersonsParams) ([]GetEngagementPersonsRow, error) {
	rows, err := q.db.Query(ctx, getEngagementPersons, arg.ID, arg.Ids)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetEngagementPersonsRow
	for rows.Next() {
		var i GetEngagementPersonsRow
		if err := rows.Scan(
			&i.NodeID,
			&i.NodeGroupID,
			&i.NodeGroupName,
			&i.NodeGroupIsActive,
			&i.NodeGroupCreatedAt,
			&i.NodeGroupUpdatedAt,
			&i.Title,
			&i.FirstName,
			&i.LastName,
			&i.Email,
			&i.Company,
			&i.Title_2,
			&i.ClientName,
			&i.NodeCreatedAt,
			&i.NodeUpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getEngagementStatus = `-- name: GetEngagementStatus :one
SELECT status FROM engagements as e
WHERE e.id = $1
`

func (q *Queries) GetEngagementStatus(ctx context.Context, id pgtype.UUID) (EngagementStatusEnum, error) {
	row := q.db.QueryRow(ctx, getEngagementStatus, id)
	var status EngagementStatusEnum
	err := row.Scan(&status)
	return status, err
}

const getEngagementTree = `-- name: GetEngagementTree :many
SELECT node_groups.id                        as node_group_id,
       node_groups.name                      as node_group_name,
       node_groups.is_active                 as node_group_is_active,
       node_groups.created_at                as node_group_created_at,
       node_groups.updated_at                as node_group_updated_at,
       nodes.node_type,
       nodes.name                            as node_name,
       nodes.id                              as node_id,
       MIN(logs_nodes.created_at)::TIMESTAMP AS node_created_at,
       MAX(logs_nodes.created_at)::TIMESTAMP AS node_updated_at
FROM nodes
         JOIN node_groups on nodes.node_group_id = node_groups.id
         JOIN engagements on node_groups.engagement_id = engagements.id
         JOIN engagements_users on engagements.id = engagements_users.engagement_id
         JOIN users on engagements_users.user_id = users.id
         LEFT JOIN logs_nodes on nodes.id = logs_nodes.node_id
WHERE engagements.id = $1
  AND users.id = $2
  AND is_deleted = false
GROUP BY node_groups.id, nodes.id, engagements.id, users.id
`

type GetEngagementTreeParams struct {
	ID   pgtype.UUID `json:"id"`
	ID_2 pgtype.UUID `json:"id_2"`
}

type GetEngagementTreeRow struct {
	NodeGroupID        pgtype.UUID      `json:"node_group_id"`
	NodeGroupName      string           `json:"node_group_name"`
	NodeGroupIsActive  bool             `json:"node_group_is_active"`
	NodeGroupCreatedAt pgtype.Timestamp `json:"node_group_created_at"`
	NodeGroupUpdatedAt pgtype.Timestamp `json:"node_group_updated_at"`
	NodeType           NodeTypeEnum     `json:"node_type"`
	NodeName           string           `json:"node_name"`
	NodeID             pgtype.UUID      `json:"node_id"`
	NodeCreatedAt      pgtype.Timestamp `json:"node_created_at"`
	NodeUpdatedAt      pgtype.Timestamp `json:"node_updated_at"`
}

func (q *Queries) GetEngagementTree(ctx context.Context, arg GetEngagementTreeParams) ([]GetEngagementTreeRow, error) {
	rows, err := q.db.Query(ctx, getEngagementTree, arg.ID, arg.ID_2)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetEngagementTreeRow
	for rows.Next() {
		var i GetEngagementTreeRow
		if err := rows.Scan(
			&i.NodeGroupID,
			&i.NodeGroupName,
			&i.NodeGroupIsActive,
			&i.NodeGroupCreatedAt,
			&i.NodeGroupUpdatedAt,
			&i.NodeType,
			&i.NodeName,
			&i.NodeID,
			&i.NodeCreatedAt,
			&i.NodeUpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getEngagementUrls = `-- name: GetEngagementUrls :many
SELECT n.id                                  as node_id,
       ng.id                                 as node_group_id,
       ng.name                               as node_group_name,
       ng.is_active                          as node_group_is_active,
       ng.created_at                         as node_group_created_at,
       ng.updated_at                         as node_group_updated_at,
       engagements.title,
       node_type_urls.url,
       clients.name                          as client_name,
       MIN(logs_nodes.created_at)::TIMESTAMP AS node_created_at,
       MAX(logs_nodes.created_at)::TIMESTAMP AS node_updated_at
FROM node_type_urls
         JOIN nodes n on n.id = node_type_urls.node_id
         JOIN node_groups ng on n.node_group_id = ng.id
         JOIN engagements on ng.engagement_id = engagements.id
         JOIN engagements_users on engagements.id = engagements_users.engagement_id
         JOIN users on engagements_users.user_id = users.id
         JOIN clients on engagements.client_id = clients.id
         LEFT JOIN logs_nodes on n.id = logs_nodes.node_id
WHERE engagements.id = ANY ($2::uuid[])
  AND users.id = $1
  AND n.is_deleted = false
GROUP BY n.id, ng.id, ng.name, ng.is_active, ng.created_at, ng.updated_at, engagements.title, node_type_urls.url,
         node_type_urls.node_id,
         clients.name
ORDER BY node_updated_at DESC
`

type GetEngagementUrlsParams struct {
	ID  pgtype.UUID   `json:"id"`
	Ids []pgtype.UUID `json:"ids"`
}

type GetEngagementUrlsRow struct {
	NodeID             pgtype.UUID      `json:"node_id"`
	NodeGroupID        pgtype.UUID      `json:"node_group_id"`
	NodeGroupName      string           `json:"node_group_name"`
	NodeGroupIsActive  bool             `json:"node_group_is_active"`
	NodeGroupCreatedAt pgtype.Timestamp `json:"node_group_created_at"`
	NodeGroupUpdatedAt pgtype.Timestamp `json:"node_group_updated_at"`
	Title              string           `json:"title"`
	Url                string           `json:"url"`
	ClientName         string           `json:"client_name"`
	NodeCreatedAt      pgtype.Timestamp `json:"node_created_at"`
	NodeUpdatedAt      pgtype.Timestamp `json:"node_updated_at"`
}

func (q *Queries) GetEngagementUrls(ctx context.Context, arg GetEngagementUrlsParams) ([]GetEngagementUrlsRow, error) {
	rows, err := q.db.Query(ctx, getEngagementUrls, arg.ID, arg.Ids)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetEngagementUrlsRow
	for rows.Next() {
		var i GetEngagementUrlsRow
		if err := rows.Scan(
			&i.NodeID,
			&i.NodeGroupID,
			&i.NodeGroupName,
			&i.NodeGroupIsActive,
			&i.NodeGroupCreatedAt,
			&i.NodeGroupUpdatedAt,
			&i.Title,
			&i.Url,
			&i.ClientName,
			&i.NodeCreatedAt,
			&i.NodeUpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getEngagementUsers = `-- name: GetEngagementUsers :many
SELECT id, username, custom_username, full_name, app_role, ssh_key, ssh_key_label, ssh_key_creation_date, is_inactive, engagement_id, user_id
FROM users
         JOIN engagements_users eu on users.id = eu.user_id
WHERE eu.engagement_id = $1
`

type GetEngagementUsersRow struct {
	ID                 pgtype.UUID      `json:"id"`
	Username           string           `json:"username"`
	CustomUsername     pgtype.Text      `json:"custom_username"`
	FullName           pgtype.Text      `json:"full_name"`
	AppRole            pgtype.Text      `json:"app_role"`
	SshKey             pgtype.Text      `json:"ssh_key"`
	SshKeyLabel        pgtype.Text      `json:"ssh_key_label"`
	SshKeyCreationDate pgtype.Timestamp `json:"ssh_key_creation_date"`
	IsInactive         pgtype.Bool      `json:"is_inactive"`
	EngagementID       pgtype.UUID      `json:"engagement_id"`
	UserID             pgtype.UUID      `json:"user_id"`
}

func (q *Queries) GetEngagementUsers(ctx context.Context, engagementID pgtype.UUID) ([]GetEngagementUsersRow, error) {
	rows, err := q.db.Query(ctx, getEngagementUsers, engagementID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetEngagementUsersRow
	for rows.Next() {
		var i GetEngagementUsersRow
		if err := rows.Scan(
			&i.ID,
			&i.Username,
			&i.CustomUsername,
			&i.FullName,
			&i.AppRole,
			&i.SshKey,
			&i.SshKeyLabel,
			&i.SshKeyCreationDate,
			&i.IsInactive,
			&i.EngagementID,
			&i.UserID,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getInstanceSizeMappingsForCategory = `-- name: GetInstanceSizeMappingsForCategory :many
SELECT id, provider, size_alias, priority, instance_type
FROM instance_size_mappings
WHERE provider = $1
AND size_alias = $2
ORDER BY provider, size_alias, priority
`

type GetInstanceSizeMappingsForCategoryParams struct {
	Provider  ProviderEnum `json:"provider"`
	SizeAlias string       `json:"size_alias"`
}

func (q *Queries) GetInstanceSizeMappingsForCategory(ctx context.Context, arg GetInstanceSizeMappingsForCategoryParams) ([]InstanceSizeMapping, error) {
	rows, err := q.db.Query(ctx, getInstanceSizeMappingsForCategory, arg.Provider, arg.SizeAlias)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []InstanceSizeMapping
	for rows.Next() {
		var i InstanceSizeMapping
		if err := rows.Scan(
			&i.ID,
			&i.Provider,
			&i.SizeAlias,
			&i.Priority,
			&i.InstanceType,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getInstanceSizeMappingsForProvider = `-- name: GetInstanceSizeMappingsForProvider :many
SELECT size_alias, priority, instance_type
FROM instance_size_mappings
WHERE instance_size_mappings.provider = $1
  AND priority = $2
`

type GetInstanceSizeMappingsForProviderParams struct {
	Provider ProviderEnum `json:"provider"`
	Priority int32        `json:"priority"`
}

type GetInstanceSizeMappingsForProviderRow struct {
	SizeAlias    string `json:"size_alias"`
	Priority     int32  `json:"priority"`
	InstanceType string `json:"instance_type"`
}

func (q *Queries) GetInstanceSizeMappingsForProvider(ctx context.Context, arg GetInstanceSizeMappingsForProviderParams) ([]GetInstanceSizeMappingsForProviderRow, error) {
	rows, err := q.db.Query(ctx, getInstanceSizeMappingsForProvider, arg.Provider, arg.Priority)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetInstanceSizeMappingsForProviderRow
	for rows.Next() {
		var i GetInstanceSizeMappingsForProviderRow
		if err := rows.Scan(&i.SizeAlias, &i.Priority, &i.InstanceType); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getInstanceSizeMappingsForRegionCheck = `-- name: GetInstanceSizeMappingsForRegionCheck :many
SELECT DISTINCT instance_type, size_alias, priority
FROM instance_size_mappings
WHERE provider = $1
ORDER BY priority
`

type GetInstanceSizeMappingsForRegionCheckRow struct {
	InstanceType string `json:"instance_type"`
	SizeAlias    string `json:"size_alias"`
	Priority     int32  `json:"priority"`
}

func (q *Queries) GetInstanceSizeMappingsForRegionCheck(ctx context.Context, provider ProviderEnum) ([]GetInstanceSizeMappingsForRegionCheckRow, error) {
	rows, err := q.db.Query(ctx, getInstanceSizeMappingsForRegionCheck, provider)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetInstanceSizeMappingsForRegionCheckRow
	for rows.Next() {
		var i GetInstanceSizeMappingsForRegionCheckRow
		if err := rows.Scan(&i.InstanceType, &i.SizeAlias, &i.Priority); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getInstanceSizePrioritiesForProvider = `-- name: GetInstanceSizePrioritiesForProvider :many
SELECT DISTINCT(priority)
FROM instance_size_mappings
WHERE instance_size_mappings.provider = $1
ORDER BY priority
`

func (q *Queries) GetInstanceSizePrioritiesForProvider(ctx context.Context, provider ProviderEnum) ([]int32, error) {
	rows, err := q.db.Query(ctx, getInstanceSizePrioritiesForProvider, provider)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []int32
	for rows.Next() {
		var priority int32
		if err := rows.Scan(&priority); err != nil {
			return nil, err
		}
		items = append(items, priority)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getNodeActivityLogs = `-- name: GetNodeActivityLogs :many
SELECT message, type, u.username, created_at
FROM logs_nodes
         JOIN users u on logs_nodes.user_id = u.id
WHERE node_id = $1
`

type GetNodeActivityLogsRow struct {
	Message   string            `json:"message"`
	Type      LogsNodesTypeEnum `json:"type"`
	Username  string            `json:"username"`
	CreatedAt pgtype.Timestamp  `json:"created_at"`
}

func (q *Queries) GetNodeActivityLogs(ctx context.Context, nodeID pgtype.UUID) ([]GetNodeActivityLogsRow, error) {
	rows, err := q.db.Query(ctx, getNodeActivityLogs, nodeID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetNodeActivityLogsRow
	for rows.Next() {
		var i GetNodeActivityLogsRow
		if err := rows.Scan(
			&i.Message,
			&i.Type,
			&i.Username,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getNodeGroupId = `-- name: GetNodeGroupId :one
SELECT node_group_id
FROM nodes
WHERE id = $1
`

func (q *Queries) GetNodeGroupId(ctx context.Context, id pgtype.UUID) (pgtype.UUID, error) {
	row := q.db.QueryRow(ctx, getNodeGroupId, id)
	var node_group_id pgtype.UUID
	err := row.Scan(&node_group_id)
	return node_group_id, err
}

const getNodeIDByDomainURL = `-- name: GetNodeIDByDomainURL :one
SELECT n.id as node_id
FROM nodes n
JOIN node_type_urls ntu ON n.id = ntu.node_id
JOIN domains d ON LOWER(d.url) = LOWER(ntu.url)
WHERE d.id = $1
LIMIT 1
`

func (q *Queries) GetNodeIDByDomainURL(ctx context.Context, id pgtype.UUID) (pgtype.UUID, error) {
	row := q.db.QueryRow(ctx, getNodeIDByDomainURL, id)
	var node_id pgtype.UUID
	err := row.Scan(&node_id)
	return node_id, err
}

const getNodeIDByURL = `-- name: GetNodeIDByURL :one
SELECT n.id as node_id
FROM nodes n
JOIN node_type_urls ntu ON n.id = ntu.node_id
WHERE LOWER(ntu.url) = LOWER($1)
LIMIT 1
`

func (q *Queries) GetNodeIDByURL(ctx context.Context, lower string) (pgtype.UUID, error) {
	row := q.db.QueryRow(ctx, getNodeIDByURL, lower)
	var node_id pgtype.UUID
	err := row.Scan(&node_id)
	return node_id, err
}

const getNodeTypeAndName = `-- name: GetNodeTypeAndName :one
SELECT node_type, name
FROM nodes
WHERE id = $1
`

type GetNodeTypeAndNameRow struct {
	NodeType NodeTypeEnum `json:"node_type"`
	Name     string       `json:"name"`
}

func (q *Queries) GetNodeTypeAndName(ctx context.Context, id pgtype.UUID) (GetNodeTypeAndNameRow, error) {
	row := q.db.QueryRow(ctx, getNodeTypeAndName, id)
	var i GetNodeTypeAndNameRow
	err := row.Scan(&i.NodeType, &i.Name)
	return i, err
}

const getNodeTypeCloudInstance = `-- name: GetNodeTypeCloudInstance :one
SELECT provider,
	   region,
	   operating_system_image_id,
	   node_type_cloud_instances.instance_type          as instance_size,
	   node_type_cloud_instances.name                   as instance_name,
	   open_ports,
	   public_ipv4_address,
	   node_type_cloud_instances.node_id,
	   node_type,
	   node_group_id,
	   n.is_deleted                                     as node_is_deleted,
	   ng.name                                          as node_group_name,
	   ng.is_active                                     as node_group_is_active,
	   ng.created_at                                    as node_group_created_at,
	   ng.updated_at                                    as node_group_updated_at,
	   e.id                                             as engagement_id,
	   e.is_active                                      as engagement_is_active,
	   e.status                                         as engagement_status,
	   u.id                                             as user_id,
	   u.username,
     COALESCE(d.status, 'ERROR')                     AS CI_deployment_status,
     cloud_instance_state,
     cloud_instance_id
FROM node_type_cloud_instances
         JOIN nodes n ON n.id = node_type_cloud_instances.node_id
         JOIN node_groups ng ON n.node_group_id = ng.id
         JOIN engagements e ON ng.engagement_id = e.id
         JOIN engagements_users ON e.id = engagements_users.engagement_id
         JOIN users u ON engagements_users.user_id = u.id
         LEFT JOIN (
              SELECT DISTINCT ON (deployments.node_id)
                      deployments.node_id,
                      deployments.status
              FROM deployments
              ORDER BY deployments.node_id, deployments.created_at DESC
         ) d ON d.node_id = node_type_cloud_instances.node_id
WHERE node_type_cloud_instances.node_id = $1
  AND u.id = $2
  AND n.is_deleted = false
`

type GetNodeTypeCloudInstanceParams struct {
	NodeID pgtype.UUID `json:"node_id"`
	ID     pgtype.UUID `json:"id"`
}

type GetNodeTypeCloudInstanceRow struct {
	Provider               ProviderEnum         `json:"provider"`
	Region                 string               `json:"region"`
	OperatingSystemImageID string               `json:"operating_system_image_id"`
	InstanceSize           string               `json:"instance_size"`
	InstanceName           string               `json:"instance_name"`
	OpenPorts              []int32              `json:"open_ports"`
	PublicIpv4Address      *netip.Addr          `json:"public_ipv4_address"`
	NodeID                 pgtype.UUID          `json:"node_id"`
	NodeType               NodeTypeEnum         `json:"node_type"`
	NodeGroupID            pgtype.UUID          `json:"node_group_id"`
	NodeIsDeleted          bool                 `json:"node_is_deleted"`
	NodeGroupName          string               `json:"node_group_name"`
	NodeGroupIsActive      bool                 `json:"node_group_is_active"`
	NodeGroupCreatedAt     pgtype.Timestamp     `json:"node_group_created_at"`
	NodeGroupUpdatedAt     pgtype.Timestamp     `json:"node_group_updated_at"`
	EngagementID           pgtype.UUID          `json:"engagement_id"`
	EngagementIsActive     bool                 `json:"engagement_is_active"`
	EngagementStatus       EngagementStatusEnum `json:"engagement_status"`
	UserID                 pgtype.UUID          `json:"user_id"`
	Username               string               `json:"username"`
	CiDeploymentStatus     DeploymentStatusEnum `json:"ci_deployment_status"`
	CloudInstanceState     NullCiStateEnum      `json:"cloud_instance_state"`
	CloudInstanceID        pgtype.Text          `json:"cloud_instance_id"`
}

func (q *Queries) GetNodeTypeCloudInstance(ctx context.Context, arg GetNodeTypeCloudInstanceParams) (GetNodeTypeCloudInstanceRow, error) {
	row := q.db.QueryRow(ctx, getNodeTypeCloudInstance, arg.NodeID, arg.ID)
	var i GetNodeTypeCloudInstanceRow
	err := row.Scan(
		&i.Provider,
		&i.Region,
		&i.OperatingSystemImageID,
		&i.InstanceSize,
		&i.InstanceName,
		&i.OpenPorts,
		&i.PublicIpv4Address,
		&i.NodeID,
		&i.NodeType,
		&i.NodeGroupID,
		&i.NodeIsDeleted,
		&i.NodeGroupName,
		&i.NodeGroupIsActive,
		&i.NodeGroupCreatedAt,
		&i.NodeGroupUpdatedAt,
		&i.EngagementID,
		&i.EngagementIsActive,
		&i.EngagementStatus,
		&i.UserID,
		&i.Username,
		&i.CiDeploymentStatus,
		&i.CloudInstanceState,
		&i.CloudInstanceID,
	)
	return i, err
}

const getNodeTypeEmailAddress = `-- name: GetNodeTypeEmailAddress :one
SELECT email_address, node_id, n.id, node_type, n.name, node_group_id, is_deleted, ng.id, ng.name, ng.is_active, ng.created_at, ng.updated_at, ng.engagement_id, engagements.id, title, wbs_code, engagements.is_active, client_id, engagements.created_at, engagements.updated_at, status, error_message, engagements_users.engagement_id, user_id, users.id, username, custom_username, full_name, app_role, ssh_key, ssh_key_label, ssh_key_creation_date, is_inactive
FROM node_type_email_addresses
         JOIN nodes n on n.id = node_type_email_addresses.node_id
         JOIN node_groups ng on n.node_group_id = ng.id
         JOIN engagements on ng.engagement_id = engagements.id
         JOIN engagements_users on engagements.id = engagements_users.engagement_id
         JOIN users on engagements_users.user_id = users.id
WHERE node_id = $1
  AND users.id = $2
  AND n.is_deleted = false
`

type GetNodeTypeEmailAddressParams struct {
	NodeID pgtype.UUID `json:"node_id"`
	ID     pgtype.UUID `json:"id"`
}

type GetNodeTypeEmailAddressRow struct {
	EmailAddress       string               `json:"email_address"`
	NodeID             pgtype.UUID          `json:"node_id"`
	ID                 pgtype.UUID          `json:"id"`
	NodeType           NodeTypeEnum         `json:"node_type"`
	Name               string               `json:"name"`
	NodeGroupID        pgtype.UUID          `json:"node_group_id"`
	IsDeleted          bool                 `json:"is_deleted"`
	ID_2               pgtype.UUID          `json:"id_2"`
	Name_2             string               `json:"name_2"`
	IsActive           bool                 `json:"is_active"`
	CreatedAt          pgtype.Timestamp     `json:"created_at"`
	UpdatedAt          pgtype.Timestamp     `json:"updated_at"`
	EngagementID       pgtype.UUID          `json:"engagement_id"`
	ID_3               pgtype.UUID          `json:"id_3"`
	Title              string               `json:"title"`
	WbsCode            string               `json:"wbs_code"`
	IsActive_2         bool                 `json:"is_active_2"`
	ClientID           pgtype.UUID          `json:"client_id"`
	CreatedAt_2        pgtype.Timestamp     `json:"created_at_2"`
	UpdatedAt_2        pgtype.Timestamp     `json:"updated_at_2"`
	Status             EngagementStatusEnum `json:"status"`
	ErrorMessage       pgtype.Text          `json:"error_message"`
	EngagementID_2     pgtype.UUID          `json:"engagement_id_2"`
	UserID             pgtype.UUID          `json:"user_id"`
	ID_4               pgtype.UUID          `json:"id_4"`
	Username           string               `json:"username"`
	CustomUsername     pgtype.Text          `json:"custom_username"`
	FullName           pgtype.Text          `json:"full_name"`
	AppRole            pgtype.Text          `json:"app_role"`
	SshKey             pgtype.Text          `json:"ssh_key"`
	SshKeyLabel        pgtype.Text          `json:"ssh_key_label"`
	SshKeyCreationDate pgtype.Timestamp     `json:"ssh_key_creation_date"`
	IsInactive         pgtype.Bool          `json:"is_inactive"`
}

func (q *Queries) GetNodeTypeEmailAddress(ctx context.Context, arg GetNodeTypeEmailAddressParams) (GetNodeTypeEmailAddressRow, error) {
	row := q.db.QueryRow(ctx, getNodeTypeEmailAddress, arg.NodeID, arg.ID)
	var i GetNodeTypeEmailAddressRow
	err := row.Scan(
		&i.EmailAddress,
		&i.NodeID,
		&i.ID,
		&i.NodeType,
		&i.Name,
		&i.NodeGroupID,
		&i.IsDeleted,
		&i.ID_2,
		&i.Name_2,
		&i.IsActive,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.EngagementID,
		&i.ID_3,
		&i.Title,
		&i.WbsCode,
		&i.IsActive_2,
		&i.ClientID,
		&i.CreatedAt_2,
		&i.UpdatedAt_2,
		&i.Status,
		&i.ErrorMessage,
		&i.EngagementID_2,
		&i.UserID,
		&i.ID_4,
		&i.Username,
		&i.CustomUsername,
		&i.FullName,
		&i.AppRole,
		&i.SshKey,
		&i.SshKeyLabel,
		&i.SshKeyCreationDate,
		&i.IsInactive,
	)
	return i, err
}

const getNodeTypeHost = `-- name: GetNodeTypeHost :one
SELECT node_type_hosts.name, ip_addresses, alternative_names, node_id, n.id, node_type, n.name, node_group_id, is_deleted, ng.id, ng.name, ng.is_active, ng.created_at, ng.updated_at, ng.engagement_id, engagements.id, title, wbs_code, engagements.is_active, client_id, engagements.created_at, engagements.updated_at, status, error_message, engagements_users.engagement_id, user_id, users.id, username, custom_username, full_name, app_role, ssh_key, ssh_key_label, ssh_key_creation_date, is_inactive
FROM node_type_hosts
         JOIN nodes n on n.id = node_type_hosts.node_id
         JOIN node_groups ng on n.node_group_id = ng.id
         JOIN engagements on ng.engagement_id = engagements.id
         JOIN engagements_users on engagements.id = engagements_users.engagement_id
         JOIN users on engagements_users.user_id = users.id
WHERE node_id = $1
  AND users.id = $2
  AND n.is_deleted = false
`

type GetNodeTypeHostParams struct {
	NodeID pgtype.UUID `json:"node_id"`
	ID     pgtype.UUID `json:"id"`
}

type GetNodeTypeHostRow struct {
	Name               string               `json:"name"`
	IpAddresses        []netip.Addr         `json:"ip_addresses"`
	AlternativeNames   []string             `json:"alternative_names"`
	NodeID             pgtype.UUID          `json:"node_id"`
	ID                 pgtype.UUID          `json:"id"`
	NodeType           NodeTypeEnum         `json:"node_type"`
	Name_2             string               `json:"name_2"`
	NodeGroupID        pgtype.UUID          `json:"node_group_id"`
	IsDeleted          bool                 `json:"is_deleted"`
	ID_2               pgtype.UUID          `json:"id_2"`
	Name_3             string               `json:"name_3"`
	IsActive           bool                 `json:"is_active"`
	CreatedAt          pgtype.Timestamp     `json:"created_at"`
	UpdatedAt          pgtype.Timestamp     `json:"updated_at"`
	EngagementID       pgtype.UUID          `json:"engagement_id"`
	ID_3               pgtype.UUID          `json:"id_3"`
	Title              string               `json:"title"`
	WbsCode            string               `json:"wbs_code"`
	IsActive_2         bool                 `json:"is_active_2"`
	ClientID           pgtype.UUID          `json:"client_id"`
	CreatedAt_2        pgtype.Timestamp     `json:"created_at_2"`
	UpdatedAt_2        pgtype.Timestamp     `json:"updated_at_2"`
	Status             EngagementStatusEnum `json:"status"`
	ErrorMessage       pgtype.Text          `json:"error_message"`
	EngagementID_2     pgtype.UUID          `json:"engagement_id_2"`
	UserID             pgtype.UUID          `json:"user_id"`
	ID_4               pgtype.UUID          `json:"id_4"`
	Username           string               `json:"username"`
	CustomUsername     pgtype.Text          `json:"custom_username"`
	FullName           pgtype.Text          `json:"full_name"`
	AppRole            pgtype.Text          `json:"app_role"`
	SshKey             pgtype.Text          `json:"ssh_key"`
	SshKeyLabel        pgtype.Text          `json:"ssh_key_label"`
	SshKeyCreationDate pgtype.Timestamp     `json:"ssh_key_creation_date"`
	IsInactive         pgtype.Bool          `json:"is_inactive"`
}

func (q *Queries) GetNodeTypeHost(ctx context.Context, arg GetNodeTypeHostParams) (GetNodeTypeHostRow, error) {
	row := q.db.QueryRow(ctx, getNodeTypeHost, arg.NodeID, arg.ID)
	var i GetNodeTypeHostRow
	err := row.Scan(
		&i.Name,
		&i.IpAddresses,
		&i.AlternativeNames,
		&i.NodeID,
		&i.ID,
		&i.NodeType,
		&i.Name_2,
		&i.NodeGroupID,
		&i.IsDeleted,
		&i.ID_2,
		&i.Name_3,
		&i.IsActive,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.EngagementID,
		&i.ID_3,
		&i.Title,
		&i.WbsCode,
		&i.IsActive_2,
		&i.ClientID,
		&i.CreatedAt_2,
		&i.UpdatedAt_2,
		&i.Status,
		&i.ErrorMessage,
		&i.EngagementID_2,
		&i.UserID,
		&i.ID_4,
		&i.Username,
		&i.CustomUsername,
		&i.FullName,
		&i.AppRole,
		&i.SshKey,
		&i.SshKeyLabel,
		&i.SshKeyCreationDate,
		&i.IsInactive,
	)
	return i, err
}

const getNodeTypePerson = `-- name: GetNodeTypePerson :one
SELECT first_name, last_name, email, company, node_type_persons.title, node_id, n.id, node_type, n.name, node_group_id, is_deleted, ng.id, ng.name, ng.is_active, ng.created_at, ng.updated_at, ng.engagement_id, engagements.id, engagements.title, wbs_code, engagements.is_active, client_id, engagements.created_at, engagements.updated_at, status, error_message, engagements_users.engagement_id, user_id, users.id, username, custom_username, full_name, app_role, ssh_key, ssh_key_label, ssh_key_creation_date, is_inactive
FROM node_type_persons
         JOIN nodes n on n.id = node_type_persons.node_id
         JOIN node_groups ng on n.node_group_id = ng.id
         JOIN engagements on ng.engagement_id = engagements.id
         JOIN engagements_users on engagements.id = engagements_users.engagement_id
         JOIN users on engagements_users.user_id = users.id
WHERE node_id = $1
  AND users.id = $2
  AND n.is_deleted = false
`

type GetNodeTypePersonParams struct {
	NodeID pgtype.UUID `json:"node_id"`
	ID     pgtype.UUID `json:"id"`
}

type GetNodeTypePersonRow struct {
	FirstName          string               `json:"first_name"`
	LastName           pgtype.Text          `json:"last_name"`
	Email              pgtype.Text          `json:"email"`
	Company            pgtype.Text          `json:"company"`
	Title              pgtype.Text          `json:"title"`
	NodeID             pgtype.UUID          `json:"node_id"`
	ID                 pgtype.UUID          `json:"id"`
	NodeType           NodeTypeEnum         `json:"node_type"`
	Name               string               `json:"name"`
	NodeGroupID        pgtype.UUID          `json:"node_group_id"`
	IsDeleted          bool                 `json:"is_deleted"`
	ID_2               pgtype.UUID          `json:"id_2"`
	Name_2             string               `json:"name_2"`
	IsActive           bool                 `json:"is_active"`
	CreatedAt          pgtype.Timestamp     `json:"created_at"`
	UpdatedAt          pgtype.Timestamp     `json:"updated_at"`
	EngagementID       pgtype.UUID          `json:"engagement_id"`
	ID_3               pgtype.UUID          `json:"id_3"`
	Title_2            string               `json:"title_2"`
	WbsCode            string               `json:"wbs_code"`
	IsActive_2         bool                 `json:"is_active_2"`
	ClientID           pgtype.UUID          `json:"client_id"`
	CreatedAt_2        pgtype.Timestamp     `json:"created_at_2"`
	UpdatedAt_2        pgtype.Timestamp     `json:"updated_at_2"`
	Status             EngagementStatusEnum `json:"status"`
	ErrorMessage       pgtype.Text          `json:"error_message"`
	EngagementID_2     pgtype.UUID          `json:"engagement_id_2"`
	UserID             pgtype.UUID          `json:"user_id"`
	ID_4               pgtype.UUID          `json:"id_4"`
	Username           string               `json:"username"`
	CustomUsername     pgtype.Text          `json:"custom_username"`
	FullName           pgtype.Text          `json:"full_name"`
	AppRole            pgtype.Text          `json:"app_role"`
	SshKey             pgtype.Text          `json:"ssh_key"`
	SshKeyLabel        pgtype.Text          `json:"ssh_key_label"`
	SshKeyCreationDate pgtype.Timestamp     `json:"ssh_key_creation_date"`
	IsInactive         pgtype.Bool          `json:"is_inactive"`
}

func (q *Queries) GetNodeTypePerson(ctx context.Context, arg GetNodeTypePersonParams) (GetNodeTypePersonRow, error) {
	row := q.db.QueryRow(ctx, getNodeTypePerson, arg.NodeID, arg.ID)
	var i GetNodeTypePersonRow
	err := row.Scan(
		&i.FirstName,
		&i.LastName,
		&i.Email,
		&i.Company,
		&i.Title,
		&i.NodeID,
		&i.ID,
		&i.NodeType,
		&i.Name,
		&i.NodeGroupID,
		&i.IsDeleted,
		&i.ID_2,
		&i.Name_2,
		&i.IsActive,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.EngagementID,
		&i.ID_3,
		&i.Title_2,
		&i.WbsCode,
		&i.IsActive_2,
		&i.ClientID,
		&i.CreatedAt_2,
		&i.UpdatedAt_2,
		&i.Status,
		&i.ErrorMessage,
		&i.EngagementID_2,
		&i.UserID,
		&i.ID_4,
		&i.Username,
		&i.CustomUsername,
		&i.FullName,
		&i.AppRole,
		&i.SshKey,
		&i.SshKeyLabel,
		&i.SshKeyCreationDate,
		&i.IsInactive,
	)
	return i, err
}

const getNodeTypeUrl = `-- name: GetNodeTypeUrl :one
SELECT
    node_type_urls.url,
    node_type_urls.node_id,
    n.id,
    n.node_type,
    n.name,
    n.node_group_id,
    n.is_deleted,
    ng.id,
    ng.name,
    ng.is_active,
    ng.created_at,
    ng.updated_at,
    ng.engagement_id,
    engagements.id,
    engagements.title,
    engagements.wbs_code,
    engagements.is_active,
    engagements.client_id,
    engagements.created_at,
    engagements.updated_at,
    engagements.status,
    engagements.error_message,
    engagements_users.engagement_id,
    engagements_users.user_id,
    users.id,
    users.username,
    users.custom_username,
    users.full_name,
    users.app_role,
    users.ssh_key,
    users.ssh_key_label,
    users.ssh_key_creation_date,
    users.is_inactive
FROM node_type_urls
         JOIN nodes n on n.id = node_type_urls.node_id
         JOIN node_groups ng on n.node_group_id = ng.id
         JOIN engagements on ng.engagement_id = engagements.id
         JOIN engagements_users on engagements.id = engagements_users.engagement_id
         JOIN users on engagements_users.user_id = users.id
WHERE node_id = $1
  AND users.id = $2
  AND n.is_deleted = false
`

type GetNodeTypeUrlParams struct {
	NodeID pgtype.UUID `json:"node_id"`
	ID     pgtype.UUID `json:"id"`
}

type GetNodeTypeUrlRow struct {
	Url                string               `json:"url"`
	NodeID             pgtype.UUID          `json:"node_id"`
	ID                 pgtype.UUID          `json:"id"`
	NodeType           NodeTypeEnum         `json:"node_type"`
	Name               string               `json:"name"`
	NodeGroupID        pgtype.UUID          `json:"node_group_id"`
	IsDeleted          bool                 `json:"is_deleted"`
	ID_2               pgtype.UUID          `json:"id_2"`
	Name_2             string               `json:"name_2"`
	IsActive           bool                 `json:"is_active"`
	CreatedAt          pgtype.Timestamp     `json:"created_at"`
	UpdatedAt          pgtype.Timestamp     `json:"updated_at"`
	EngagementID       pgtype.UUID          `json:"engagement_id"`
	ID_3               pgtype.UUID          `json:"id_3"`
	Title              string               `json:"title"`
	WbsCode            string               `json:"wbs_code"`
	IsActive_2         bool                 `json:"is_active_2"`
	ClientID           pgtype.UUID          `json:"client_id"`
	CreatedAt_2        pgtype.Timestamp     `json:"created_at_2"`
	UpdatedAt_2        pgtype.Timestamp     `json:"updated_at_2"`
	Status             EngagementStatusEnum `json:"status"`
	ErrorMessage       pgtype.Text          `json:"error_message"`
	EngagementID_2     pgtype.UUID          `json:"engagement_id_2"`
	UserID             pgtype.UUID          `json:"user_id"`
	ID_4               pgtype.UUID          `json:"id_4"`
	Username           string               `json:"username"`
	CustomUsername     pgtype.Text          `json:"custom_username"`
	FullName           pgtype.Text          `json:"full_name"`
	AppRole            pgtype.Text          `json:"app_role"`
	SshKey             pgtype.Text          `json:"ssh_key"`
	SshKeyLabel        pgtype.Text          `json:"ssh_key_label"`
	SshKeyCreationDate pgtype.Timestamp     `json:"ssh_key_creation_date"`
	IsInactive         pgtype.Bool          `json:"is_inactive"`
}

func (q *Queries) GetNodeTypeUrl(ctx context.Context, arg GetNodeTypeUrlParams) (GetNodeTypeUrlRow, error) {
	row := q.db.QueryRow(ctx, getNodeTypeUrl, arg.NodeID, arg.ID)
	var i GetNodeTypeUrlRow
	err := row.Scan(
		&i.Url,
		&i.NodeID,
		&i.ID,
		&i.NodeType,
		&i.Name,
		&i.NodeGroupID,
		&i.IsDeleted,
		&i.ID_2,
		&i.Name_2,
		&i.IsActive,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.EngagementID,
		&i.ID_3,
		&i.Title,
		&i.WbsCode,
		&i.IsActive_2,
		&i.ClientID,
		&i.CreatedAt_2,
		&i.UpdatedAt_2,
		&i.Status,
		&i.ErrorMessage,
		&i.EngagementID_2,
		&i.UserID,
		&i.ID_4,
		&i.Username,
		&i.CustomUsername,
		&i.FullName,
		&i.AppRole,
		&i.SshKey,
		&i.SshKeyLabel,
		&i.SshKeyCreationDate,
		&i.IsInactive,
	)
	return i, err
}

const getNodeTypes = `-- name: GetNodeTypes :many
SELECT node_type, COUNT(*)
FROM nodes
         JOIN node_groups on node_groups.id = nodes.node_group_id
         JOIN engagements on node_groups.engagement_id = engagements.id
         JOIN engagements_users on engagements.id = engagements_users.engagement_id
         JOIN users on engagements_users.user_id = users.id
WHERE users.id = $1
  AND nodes.is_deleted = false
GROUP BY node_type
`

type GetNodeTypesRow struct {
	NodeType NodeTypeEnum `json:"node_type"`
	Count    int64        `json:"count"`
}

func (q *Queries) GetNodeTypes(ctx context.Context, id pgtype.UUID) ([]GetNodeTypesRow, error) {
	rows, err := q.db.Query(ctx, getNodeTypes, id)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetNodeTypesRow
	for rows.Next() {
		var i GetNodeTypesRow
		if err := rows.Scan(&i.NodeType, &i.Count); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getNodes = `-- name: GetNodes :many
SELECT target_node_id
FROM node_relationships
WHERE source_node_id = $1
`

func (q *Queries) GetNodes(ctx context.Context, sourceNodeID pgtype.UUID) ([]pgtype.UUID, error) {
	rows, err := q.db.Query(ctx, getNodes, sourceNodeID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []pgtype.UUID
	for rows.Next() {
		var target_node_id pgtype.UUID
		if err := rows.Scan(&target_node_id); err != nil {
			return nil, err
		}
		items = append(items, target_node_id)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getNodesByNameAndType = `-- name: GetNodesByNameAndType :many
SELECT id, node_type, name, node_group_id, is_deleted
FROM nodes
WHERE name = $1 AND node_type = $2
`

type GetNodesByNameAndTypeParams struct {
	Name     string       `json:"name"`
	NodeType NodeTypeEnum `json:"node_type"`
}

func (q *Queries) GetNodesByNameAndType(ctx context.Context, arg GetNodesByNameAndTypeParams) ([]Node, error) {
	rows, err := q.db.Query(ctx, getNodesByNameAndType, arg.Name, arg.NodeType)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Node
	for rows.Next() {
		var i Node
		if err := rows.Scan(
			&i.ID,
			&i.NodeType,
			&i.Name,
			&i.NodeGroupID,
			&i.IsDeleted,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getPendingDeployment = `-- name: GetPendingDeployment :one
SELECT id, terraform_module, status, created_at, error_message, user_id, node_id, engagement_id
FROM deployments as d
WHERE d.status = 'PENDING'
  AND d.engagement_id = $1
ORDER BY created_at
LIMIT 1
`

func (q *Queries) GetPendingDeployment(ctx context.Context, engagementID pgtype.UUID) (Deployment, error) {
	row := q.db.QueryRow(ctx, getPendingDeployment, engagementID)
	var i Deployment
	err := row.Scan(
		&i.ID,
		&i.TerraformModule,
		&i.Status,
		&i.CreatedAt,
		&i.ErrorMessage,
		&i.UserID,
		&i.NodeID,
		&i.EngagementID,
	)
	return i, err
}

const getPrioritizedRegions = `-- name: GetPrioritizedRegions :many
SELECT id, name
FROM prioritized_regions
`

func (q *Queries) GetPrioritizedRegions(ctx context.Context) ([]PrioritizedRegion, error) {
	rows, err := q.db.Query(ctx, getPrioritizedRegions)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []PrioritizedRegion
	for rows.Next() {
		var i PrioritizedRegion
		if err := rows.Scan(&i.ID, &i.Name); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getScriptsByUser = `-- name: GetScriptsByUser :many
SELECT id, name, description, content, script_type, created_at, updated_at, user_id
FROM scripts
WHERE user_id = $1
`

func (q *Queries) GetScriptsByUser(ctx context.Context, userID pgtype.UUID) ([]Script, error) {
	rows, err := q.db.Query(ctx, getScriptsByUser, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Script
	for rows.Next() {
		var i Script
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Description,
			&i.Content,
			&i.ScriptType,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.UserID,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getSourceRelationships = `-- name: GetSourceRelationships :many
SELECT source_node_id
FROM node_relationships
WHERE target_node_id = $1
`

func (q *Queries) GetSourceRelationships(ctx context.Context, targetNodeID pgtype.UUID) ([]pgtype.UUID, error) {
	rows, err := q.db.Query(ctx, getSourceRelationships, targetNodeID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []pgtype.UUID
	for rows.Next() {
		var source_node_id pgtype.UUID
		if err := rows.Scan(&source_node_id); err != nil {
			return nil, err
		}
		items = append(items, source_node_id)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getStandardDeployments = `-- name: GetStandardDeployments :many
SELECT deployments.id, deployments.terraform_module, deployments.status, deployments.created_at, deployments.error_message, deployments.user_id, deployments.node_id, deployments.engagement_id,
       engagements.title,
       users.username,
       nodes.name AS node_name
FROM deployments
         JOIN engagements ON deployments.engagement_id = engagements.id
         JOIN users ON deployments.user_id = users.id
         JOIN nodes ON deployments.node_id = nodes.id
WHERE users.id = $1
`

type GetStandardDeploymentsRow struct {
	ID              pgtype.UUID          `json:"id"`
	TerraformModule string               `json:"terraform_module"`
	Status          DeploymentStatusEnum `json:"status"`
	CreatedAt       pgtype.Timestamp     `json:"created_at"`
	ErrorMessage    pgtype.Text          `json:"error_message"`
	UserID          pgtype.UUID          `json:"user_id"`
	NodeID          pgtype.UUID          `json:"node_id"`
	EngagementID    pgtype.UUID          `json:"engagement_id"`
	Title           string               `json:"title"`
	Username        string               `json:"username"`
	NodeName        string               `json:"node_name"`
}

func (q *Queries) GetStandardDeployments(ctx context.Context, id pgtype.UUID) ([]GetStandardDeploymentsRow, error) {
	rows, err := q.db.Query(ctx, getStandardDeployments, id)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetStandardDeploymentsRow
	for rows.Next() {
		var i GetStandardDeploymentsRow
		if err := rows.Scan(
			&i.ID,
			&i.TerraformModule,
			&i.Status,
			&i.CreatedAt,
			&i.ErrorMessage,
			&i.UserID,
			&i.NodeID,
			&i.EngagementID,
			&i.Title,
			&i.Username,
			&i.NodeName,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getTerraformTemplateContentByName = `-- name: GetTerraformTemplateContentByName :one
SELECT content
FROM terraform_templates
WHERE name = $1
`

func (q *Queries) GetTerraformTemplateContentByName(ctx context.Context, name string) (string, error) {
	row := q.db.QueryRow(ctx, getTerraformTemplateContentByName, name)
	var content string
	err := row.Scan(&content)
	return content, err
}

const getUniqueRegistrars = `-- name: GetUniqueRegistrars :many
SELECT DISTINCT registrar
FROM domains
WHERE registrar IS NOT NULL AND registrar != ''
ORDER BY registrar
`

func (q *Queries) GetUniqueRegistrars(ctx context.Context) ([]pgtype.Text, error) {
	rows, err := q.db.Query(ctx, getUniqueRegistrars)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []pgtype.Text
	for rows.Next() {
		var registrar pgtype.Text
		if err := rows.Scan(&registrar); err != nil {
			return nil, err
		}
		items = append(items, registrar)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getUserByAzureID = `-- name: GetUserByAzureID :one
SELECT id, username, custom_username, full_name, app_role, ssh_key, ssh_key_label, ssh_key_creation_date, is_inactive
FROM users
WHERE id = $1
`

func (q *Queries) GetUserByAzureID(ctx context.Context, id pgtype.UUID) (User, error) {
	row := q.db.QueryRow(ctx, getUserByAzureID, id)
	var i User
	err := row.Scan(
		&i.ID,
		&i.Username,
		&i.CustomUsername,
		&i.FullName,
		&i.AppRole,
		&i.SshKey,
		&i.SshKeyLabel,
		&i.SshKeyCreationDate,
		&i.IsInactive,
	)
	return i, err
}

const getUserCustomUsername = `-- name: GetUserCustomUsername :one
SELECT custom_username
FROM users
WHERE id = $1
`

func (q *Queries) GetUserCustomUsername(ctx context.Context, id pgtype.UUID) (pgtype.Text, error) {
	row := q.db.QueryRow(ctx, getUserCustomUsername, id)
	var custom_username pgtype.Text
	err := row.Scan(&custom_username)
	return custom_username, err
}

const getUserEngagements = `-- name: GetUserEngagements :many
SELECT engagements.id, title, wbs_code, is_active, client_id, created_at, updated_at, status, error_message, engagement_id, user_id, users.id, username, custom_username, full_name, app_role, ssh_key, ssh_key_label, ssh_key_creation_date, is_inactive
FROM engagements
         JOIN engagements_users on engagements.id = engagements_users.engagement_id
         JOIN users on engagements_users.user_id = users.id
WHERE users.id = $1
  AND engagements.is_active = true
`

type GetUserEngagementsRow struct {
	ID                 pgtype.UUID          `json:"id"`
	Title              string               `json:"title"`
	WbsCode            string               `json:"wbs_code"`
	IsActive           bool                 `json:"is_active"`
	ClientID           pgtype.UUID          `json:"client_id"`
	CreatedAt          pgtype.Timestamp     `json:"created_at"`
	UpdatedAt          pgtype.Timestamp     `json:"updated_at"`
	Status             EngagementStatusEnum `json:"status"`
	ErrorMessage       pgtype.Text          `json:"error_message"`
	EngagementID       pgtype.UUID          `json:"engagement_id"`
	UserID             pgtype.UUID          `json:"user_id"`
	ID_2               pgtype.UUID          `json:"id_2"`
	Username           string               `json:"username"`
	CustomUsername     pgtype.Text          `json:"custom_username"`
	FullName           pgtype.Text          `json:"full_name"`
	AppRole            pgtype.Text          `json:"app_role"`
	SshKey             pgtype.Text          `json:"ssh_key"`
	SshKeyLabel        pgtype.Text          `json:"ssh_key_label"`
	SshKeyCreationDate pgtype.Timestamp     `json:"ssh_key_creation_date"`
	IsInactive         pgtype.Bool          `json:"is_inactive"`
}

func (q *Queries) GetUserEngagements(ctx context.Context, id pgtype.UUID) ([]GetUserEngagementsRow, error) {
	rows, err := q.db.Query(ctx, getUserEngagements, id)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetUserEngagementsRow
	for rows.Next() {
		var i GetUserEngagementsRow
		if err := rows.Scan(
			&i.ID,
			&i.Title,
			&i.WbsCode,
			&i.IsActive,
			&i.ClientID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.Status,
			&i.ErrorMessage,
			&i.EngagementID,
			&i.UserID,
			&i.ID_2,
			&i.Username,
			&i.CustomUsername,
			&i.FullName,
			&i.AppRole,
			&i.SshKey,
			&i.SshKeyLabel,
			&i.SshKeyCreationDate,
			&i.IsInactive,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getUserForUserSettings = `-- name: GetUserForUserSettings :one
SELECT id, username, custom_username, full_name, app_role, ssh_key, ssh_key_label, ssh_key_creation_date, is_inactive
FROM users
WHERE id = $1
`

func (q *Queries) GetUserForUserSettings(ctx context.Context, id pgtype.UUID) (User, error) {
	row := q.db.QueryRow(ctx, getUserForUserSettings, id)
	var i User
	err := row.Scan(
		&i.ID,
		&i.Username,
		&i.CustomUsername,
		&i.FullName,
		&i.AppRole,
		&i.SshKey,
		&i.SshKeyLabel,
		&i.SshKeyCreationDate,
		&i.IsInactive,
	)
	return i, err
}

const getUserForUsername = `-- name: GetUserForUsername :one
SELECT id, username, custom_username, full_name, app_role, ssh_key, ssh_key_label, ssh_key_creation_date, is_inactive
FROM users
WHERE username = $1
`

func (q *Queries) GetUserForUsername(ctx context.Context, username string) (User, error) {
	row := q.db.QueryRow(ctx, getUserForUsername, username)
	var i User
	err := row.Scan(
		&i.ID,
		&i.Username,
		&i.CustomUsername,
		&i.FullName,
		&i.AppRole,
		&i.SshKey,
		&i.SshKeyLabel,
		&i.SshKeyCreationDate,
		&i.IsInactive,
	)
	return i, err
}

const getUserSshKey = `-- name: GetUserSshKey :one
SELECT ssh_key
FROM users
WHERE id = $1
`

func (q *Queries) GetUserSshKey(ctx context.Context, id pgtype.UUID) (pgtype.Text, error) {
	row := q.db.QueryRow(ctx, getUserSshKey, id)
	var ssh_key pgtype.Text
	err := row.Scan(&ssh_key)
	return ssh_key, err
}

const getUsers = `-- name: GetUsers :many
SELECT id, username, custom_username, full_name, app_role, ssh_key, ssh_key_label, ssh_key_creation_date, is_inactive
FROM users
`

func (q *Queries) GetUsers(ctx context.Context) ([]User, error) {
	rows, err := q.db.Query(ctx, getUsers)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []User
	for rows.Next() {
		var i User
		if err := rows.Scan(
			&i.ID,
			&i.Username,
			&i.CustomUsername,
			&i.FullName,
			&i.AppRole,
			&i.SshKey,
			&i.SshKeyLabel,
			&i.SshKeyCreationDate,
			&i.IsInactive,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getUsersFromIDs = `-- name: GetUsersFromIDs :many
SELECT id, username, custom_username, full_name, app_role, ssh_key, ssh_key_label, ssh_key_creation_date, is_inactive
FROM users
WHERE id = ANY ($1::uuid[])
`

func (q *Queries) GetUsersFromIDs(ctx context.Context, dollar_1 []pgtype.UUID) ([]User, error) {
	rows, err := q.db.Query(ctx, getUsersFromIDs, dollar_1)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []User
	for rows.Next() {
		var i User
		if err := rows.Scan(
			&i.ID,
			&i.Username,
			&i.CustomUsername,
			&i.FullName,
			&i.AppRole,
			&i.SshKey,
			&i.SshKeyLabel,
			&i.SshKeyCreationDate,
			&i.IsInactive,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getUsersWithUsername = `-- name: GetUsersWithUsername :one
SELECT COUNT(*)
FROM users
WHERE custom_username = $1
`

func (q *Queries) GetUsersWithUsername(ctx context.Context, customUsername pgtype.Text) (int64, error) {
	row := q.db.QueryRow(ctx, getUsersWithUsername, customUsername)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const getValidAdminUsers = `-- name: GetValidAdminUsers :many
SELECT id, username, custom_username, full_name, app_role, ssh_key, ssh_key_label, ssh_key_creation_date, is_inactive
FROM users
WHERE id = $1
  AND app_role = 'Admin'
  AND ssh_key IS NOT NULL
  AND custom_username IS NOT NULL
`

func (q *Queries) GetValidAdminUsers(ctx context.Context, id pgtype.UUID) ([]User, error) {
	rows, err := q.db.Query(ctx, getValidAdminUsers, id)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []User
	for rows.Next() {
		var i User
		if err := rows.Scan(
			&i.ID,
			&i.Username,
			&i.CustomUsername,
			&i.FullName,
			&i.AppRole,
			&i.SshKey,
			&i.SshKeyLabel,
			&i.SshKeyCreationDate,
			&i.IsInactive,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const insertActivityLog = `-- name: InsertActivityLog :exec
INSERT INTO logs_nodes (message, type, user_id, node_id, created_at)
VALUES ($1, $2, $3, $4, $5)
`

type InsertActivityLogParams struct {
	Message   string            `json:"message"`
	Type      LogsNodesTypeEnum `json:"type"`
	UserID    pgtype.UUID       `json:"user_id"`
	NodeID    pgtype.UUID       `json:"node_id"`
	CreatedAt pgtype.Timestamp  `json:"created_at"`
}

func (q *Queries) InsertActivityLog(ctx context.Context, arg InsertActivityLogParams) error {
	_, err := q.db.Exec(ctx, insertActivityLog,
		arg.Message,
		arg.Type,
		arg.UserID,
		arg.NodeID,
		arg.CreatedAt,
	)
	return err
}

const insertAssignmentLog = `-- name: InsertAssignmentLog :exec
INSERT INTO logs_assignments (message, type, status, user_id, user_custom_username_used, node_id)
VALUES ($1, $2, $3, $4, $5, $6)
`

type InsertAssignmentLogParams struct {
	Message                string                    `json:"message"`
	Type                   LogsAssignmentsTypeEnum   `json:"type"`
	Status                 LogsAssignmentsStatusEnum `json:"status"`
	UserID                 pgtype.UUID               `json:"user_id"`
	UserCustomUsernameUsed pgtype.Text               `json:"user_custom_username_used"`
	NodeID                 pgtype.UUID               `json:"node_id"`
}

func (q *Queries) InsertAssignmentLog(ctx context.Context, arg InsertAssignmentLogParams) error {
	_, err := q.db.Exec(ctx, insertAssignmentLog,
		arg.Message,
		arg.Type,
		arg.Status,
		arg.UserID,
		arg.UserCustomUsernameUsed,
		arg.NodeID,
	)
	return err
}

const listAWSAccountsByEngagementID = `-- name: ListAWSAccountsByEngagementID :many
SELECT a.id, a.nickname, a.cloud_account_id, a.account_cloud_status, a.account_creation_status, a.created_at, u.username AS created_by
FROM aws_accounts a
JOIN users u ON a.created_by = u.id
WHERE engagement_id = $1
ORDER BY created_at DESC
`

type ListAWSAccountsByEngagementIDRow struct {
	ID                    pgtype.UUID      `json:"id"`
	Nickname              string           `json:"nickname"`
	CloudAccountID        pgtype.Text      `json:"cloud_account_id"`
	AccountCloudStatus    pgtype.Text      `json:"account_cloud_status"`
	AccountCreationStatus pgtype.Text      `json:"account_creation_status"`
	CreatedAt             pgtype.Timestamp `json:"created_at"`
	CreatedBy             string           `json:"created_by"`
}

func (q *Queries) ListAWSAccountsByEngagementID(ctx context.Context, engagementID pgtype.UUID) ([]ListAWSAccountsByEngagementIDRow, error) {
	rows, err := q.db.Query(ctx, listAWSAccountsByEngagementID, engagementID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []ListAWSAccountsByEngagementIDRow
	for rows.Next() {
		var i ListAWSAccountsByEngagementIDRow
		if err := rows.Scan(
			&i.ID,
			&i.Nickname,
			&i.CloudAccountID,
			&i.AccountCloudStatus,
			&i.AccountCreationStatus,
			&i.CreatedAt,
			&i.CreatedBy,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listAWSAccountsByEngagementIDAndStatus = `-- name: ListAWSAccountsByEngagementIDAndStatus :many
SELECT a.id, a.nickname, a.cloud_account_id
FROM aws_accounts a
JOIN users u ON a.created_by = u.id
WHERE engagement_id = $1
  AND account_creation_status = $2
  AND account_cloud_status = $3
ORDER BY created_at DESC
`

type ListAWSAccountsByEngagementIDAndStatusParams struct {
	EngagementID          pgtype.UUID `json:"engagement_id"`
	AccountCreationStatus pgtype.Text `json:"account_creation_status"`
	AccountCloudStatus    pgtype.Text `json:"account_cloud_status"`
}

type ListAWSAccountsByEngagementIDAndStatusRow struct {
	ID             pgtype.UUID `json:"id"`
	Nickname       string      `json:"nickname"`
	CloudAccountID pgtype.Text `json:"cloud_account_id"`
}

func (q *Queries) ListAWSAccountsByEngagementIDAndStatus(ctx context.Context, arg ListAWSAccountsByEngagementIDAndStatusParams) ([]ListAWSAccountsByEngagementIDAndStatusRow, error) {
	rows, err := q.db.Query(ctx, listAWSAccountsByEngagementIDAndStatus, arg.EngagementID, arg.AccountCreationStatus, arg.AccountCloudStatus)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []ListAWSAccountsByEngagementIDAndStatusRow
	for rows.Next() {
		var i ListAWSAccountsByEngagementIDAndStatusRow
		if err := rows.Scan(&i.ID, &i.Nickname, &i.CloudAccountID); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listAzureTenantsByEngagementID = `-- name: ListAzureTenantsByEngagementID :many
SELECT id, tenant_id, subscription_id, creation_status, created_at, secrets_saved, account_cloud_status
FROM azure_tenants
WHERE engagement_id = $1
ORDER BY created_at DESC
`

type ListAzureTenantsByEngagementIDRow struct {
	ID                 pgtype.UUID      `json:"id"`
	TenantID           string           `json:"tenant_id"`
	SubscriptionID     pgtype.Text      `json:"subscription_id"`
	CreationStatus     pgtype.Text      `json:"creation_status"`
	CreatedAt          pgtype.Timestamp `json:"created_at"`
	SecretsSaved       bool             `json:"secrets_saved"`
	AccountCloudStatus pgtype.Text      `json:"account_cloud_status"`
}

func (q *Queries) ListAzureTenantsByEngagementID(ctx context.Context, engagementID pgtype.UUID) ([]ListAzureTenantsByEngagementIDRow, error) {
	rows, err := q.db.Query(ctx, listAzureTenantsByEngagementID, engagementID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []ListAzureTenantsByEngagementIDRow
	for rows.Next() {
		var i ListAzureTenantsByEngagementIDRow
		if err := rows.Scan(
			&i.ID,
			&i.TenantID,
			&i.SubscriptionID,
			&i.CreationStatus,
			&i.CreatedAt,
			&i.SecretsSaved,
			&i.AccountCloudStatus,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listAzureTenantsByEngagementIDAndStatus = `-- name: ListAzureTenantsByEngagementIDAndStatus :many
SELECT id, tenant_id
FROM azure_tenants
WHERE engagement_id = $1
    AND account_cloud_status = $2
    AND creation_status = $4
    AND subscription_id IS NOT NULL
    AND secrets_saved = $3
ORDER BY created_at DESC
`

type ListAzureTenantsByEngagementIDAndStatusParams struct {
	EngagementID       pgtype.UUID `json:"engagement_id"`
	AccountCloudStatus pgtype.Text `json:"account_cloud_status"`
	SecretsSaved       bool        `json:"secrets_saved"`
	CreationStatus     pgtype.Text `json:"creation_status"`
}

type ListAzureTenantsByEngagementIDAndStatusRow struct {
	ID       pgtype.UUID `json:"id"`
	TenantID string      `json:"tenant_id"`
}

func (q *Queries) ListAzureTenantsByEngagementIDAndStatus(ctx context.Context, arg ListAzureTenantsByEngagementIDAndStatusParams) ([]ListAzureTenantsByEngagementIDAndStatusRow, error) {
	rows, err := q.db.Query(ctx, listAzureTenantsByEngagementIDAndStatus,
		arg.EngagementID,
		arg.AccountCloudStatus,
		arg.SecretsSaved,
		arg.CreationStatus,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []ListAzureTenantsByEngagementIDAndStatusRow
	for rows.Next() {
		var i ListAzureTenantsByEngagementIDAndStatusRow
		if err := rows.Scan(&i.ID, &i.TenantID); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listAzureTenantsWithSubscriptions = `-- name: ListAzureTenantsWithSubscriptions :many
SELECT id, subscription_id, account_cloud_status
FROM azure_tenants
WHERE subscription_id IS NOT NULL
`

type ListAzureTenantsWithSubscriptionsRow struct {
	ID                 pgtype.UUID `json:"id"`
	SubscriptionID     pgtype.Text `json:"subscription_id"`
	AccountCloudStatus pgtype.Text `json:"account_cloud_status"`
}

func (q *Queries) ListAzureTenantsWithSubscriptions(ctx context.Context) ([]ListAzureTenantsWithSubscriptionsRow, error) {
	rows, err := q.db.Query(ctx, listAzureTenantsWithSubscriptions)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []ListAzureTenantsWithSubscriptionsRow
	for rows.Next() {
		var i ListAzureTenantsWithSubscriptionsRow
		if err := rows.Scan(&i.ID, &i.SubscriptionID, &i.AccountCloudStatus); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listEngagementsWithStandardUsers = `-- name: ListEngagementsWithStandardUsers :many
SELECT engagements.id,
       engagements.status,
       engagements.error_message,
       engagements.title      as engagement_title,
       engagements.wbs_code,
       engagements.is_active,
       engagements.created_at as engagement_created_at,
       engagements.updated_at as engagement_updated_at,
       clients.name           as client_name,
       users.id               as user_id,
       users.full_name,
       users.username,
       users.custom_username,
       users.ssh_key
FROM engagements
         JOIN clients on clients.id = engagements.client_id
         JOIN engagements_users on engagements.id = engagements_users.engagement_id
         JOIN users on engagements_users.user_id = users.id
WHERE users.id = $1
ORDER BY engagement_updated_at DESC
`

type ListEngagementsWithStandardUsersRow struct {
	ID                  pgtype.UUID          `json:"id"`
	Status              EngagementStatusEnum `json:"status"`
	ErrorMessage        pgtype.Text          `json:"error_message"`
	EngagementTitle     string               `json:"engagement_title"`
	WbsCode             string               `json:"wbs_code"`
	IsActive            bool                 `json:"is_active"`
	EngagementCreatedAt pgtype.Timestamp     `json:"engagement_created_at"`
	EngagementUpdatedAt pgtype.Timestamp     `json:"engagement_updated_at"`
	ClientName          string               `json:"client_name"`
	UserID              pgtype.UUID          `json:"user_id"`
	FullName            pgtype.Text          `json:"full_name"`
	Username            string               `json:"username"`
	CustomUsername      pgtype.Text          `json:"custom_username"`
	SshKey              pgtype.Text          `json:"ssh_key"`
}

func (q *Queries) ListEngagementsWithStandardUsers(ctx context.Context, id pgtype.UUID) ([]ListEngagementsWithStandardUsersRow, error) {
	rows, err := q.db.Query(ctx, listEngagementsWithStandardUsers, id)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []ListEngagementsWithStandardUsersRow
	for rows.Next() {
		var i ListEngagementsWithStandardUsersRow
		if err := rows.Scan(
			&i.ID,
			&i.Status,
			&i.ErrorMessage,
			&i.EngagementTitle,
			&i.WbsCode,
			&i.IsActive,
			&i.EngagementCreatedAt,
			&i.EngagementUpdatedAt,
			&i.ClientName,
			&i.UserID,
			&i.FullName,
			&i.Username,
			&i.CustomUsername,
			&i.SshKey,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listEngagementsWithUsers = `-- name: ListEngagementsWithUsers :many
SELECT engagements.id,
       engagements.status,
       engagements.error_message,
       engagements.title      as engagement_title,
       engagements.wbs_code,
       engagements.is_active,
       engagements.created_at as engagement_created_at,
       engagements.updated_at as engagement_updated_at,
       clients.name           as client_name,
       users.id               as user_id,
       users.full_name,
       users.username,
       users.custom_username,
       users.ssh_key
FROM engagements
         JOIN clients on clients.id = engagements.client_id
         JOIN engagements_users on engagements.id = engagements_users.engagement_id
         JOIN users on engagements_users.user_id = users.id
ORDER BY engagement_updated_at DESC
`

type ListEngagementsWithUsersRow struct {
	ID                  pgtype.UUID          `json:"id"`
	Status              EngagementStatusEnum `json:"status"`
	ErrorMessage        pgtype.Text          `json:"error_message"`
	EngagementTitle     string               `json:"engagement_title"`
	WbsCode             string               `json:"wbs_code"`
	IsActive            bool                 `json:"is_active"`
	EngagementCreatedAt pgtype.Timestamp     `json:"engagement_created_at"`
	EngagementUpdatedAt pgtype.Timestamp     `json:"engagement_updated_at"`
	ClientName          string               `json:"client_name"`
	UserID              pgtype.UUID          `json:"user_id"`
	FullName            pgtype.Text          `json:"full_name"`
	Username            string               `json:"username"`
	CustomUsername      pgtype.Text          `json:"custom_username"`
	SshKey              pgtype.Text          `json:"ssh_key"`
}

func (q *Queries) ListEngagementsWithUsers(ctx context.Context) ([]ListEngagementsWithUsersRow, error) {
	rows, err := q.db.Query(ctx, listEngagementsWithUsers)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []ListEngagementsWithUsersRow
	for rows.Next() {
		var i ListEngagementsWithUsersRow
		if err := rows.Scan(
			&i.ID,
			&i.Status,
			&i.ErrorMessage,
			&i.EngagementTitle,
			&i.WbsCode,
			&i.IsActive,
			&i.EngagementCreatedAt,
			&i.EngagementUpdatedAt,
			&i.ClientName,
			&i.UserID,
			&i.FullName,
			&i.Username,
			&i.CustomUsername,
			&i.SshKey,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const markInstancesAsTerminatedForInactiveAccounts = `-- name: MarkInstancesAsTerminatedForInactiveAccounts :exec
UPDATE node_type_cloud_instances ntci
SET cloud_instance_state = 'terminated'
FROM aws_accounts aws
WHERE ntci.aws_account_id = aws.id
  AND aws.account_cloud_status != 'ACTIVE'
  AND ntci.cloud_instance_id IS NOT NULL
  AND ntci.cloud_instance_state NOT IN ('terminated', 'error')
`

func (q *Queries) MarkInstancesAsTerminatedForInactiveAccounts(ctx context.Context) error {
	_, err := q.db.Exec(ctx, markInstancesAsTerminatedForInactiveAccounts)
	return err
}

const markInstancesAsTerminatedForInactiveAzureTenants = `-- name: MarkInstancesAsTerminatedForInactiveAzureTenants :exec
UPDATE node_type_cloud_instances
SET cloud_instance_state = 'terminated'
WHERE azure_tenant_id IN (
    SELECT id FROM azure_tenants
    WHERE account_cloud_status != 'Enabled' OR account_cloud_status IS NULL
)
AND cloud_instance_state NOT IN ('terminated', 'error')
`

func (q *Queries) MarkInstancesAsTerminatedForInactiveAzureTenants(ctx context.Context) error {
	_, err := q.db.Exec(ctx, markInstancesAsTerminatedForInactiveAzureTenants)
	return err
}

const markStuckInstancesAsFailed = `-- name: MarkStuckInstancesAsFailed :exec
UPDATE node_type_cloud_instances ntci
SET cloud_instance_state = 'error'
WHERE ntci.cloud_instance_id IS NULL
AND ntci.cloud_instance_state = 'new'
AND (
    -- Case 1: Deployment exists and is in ERROR/PENDING for too long
    EXISTS (
        SELECT 1
        FROM deployments d
        WHERE d.node_id = ntci.node_id
        AND (
            d.status = 'ERROR'
            OR (d.status = 'PENDING' AND d.created_at < NOW() - INTERVAL '10 minutes')
        )
    )
    -- Case 2: No deployment exists at all for this node_id
    OR NOT EXISTS (
        SELECT 1
        FROM deployments d
        WHERE d.node_id = ntci.node_id
    )
)
`

func (q *Queries) MarkStuckInstancesAsFailed(ctx context.Context) error {
	_, err := q.db.Exec(ctx, markStuckInstancesAsFailed)
	return err
}

const markUserInactive = `-- name: MarkUserInactive :exec
UPDATE users
SET is_inActive = false
WHERE username = $1
`

func (q *Queries) MarkUserInactive(ctx context.Context, username string) error {
	_, err := q.db.Exec(ctx, markUserInactive, username)
	return err
}

const reactivateNode = `-- name: ReactivateNode :exec
UPDATE nodes
SET is_deleted = false
WHERE id = $1
`

func (q *Queries) ReactivateNode(ctx context.Context, id pgtype.UUID) error {
	_, err := q.db.Exec(ctx, reactivateNode, id)
	return err
}

const removeUserSshKey = `-- name: RemoveUserSshKey :exec
UPDATE users
SET ssh_key               = NULL,
    ssh_key_label         = NULL,
    ssh_key_creation_date = NULL
WHERE id = $1
`

func (q *Queries) RemoveUserSshKey(ctx context.Context, id pgtype.UUID) error {
	_, err := q.db.Exec(ctx, removeUserSshKey, id)
	return err
}

const restoreEngagement = `-- name: RestoreEngagement :exec
UPDATE engagements 
SET is_active = true, 
    updated_at = NOW()
WHERE id = $1
`

func (q *Queries) RestoreEngagement(ctx context.Context, id pgtype.UUID) error {
	_, err := q.db.Exec(ctx, restoreEngagement, id)
	return err
}

const saveAzureTenantData = `-- name: SaveAzureTenantData :one
INSERT INTO azure_tenants (
    engagement_id,
    tenant_id,
    subscription_id,
    policy_id, status_id, ssh_key_public, creation_status, secrets_saved, account_cloud_status
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9
)
RETURNING id
`

type SaveAzureTenantDataParams struct {
	EngagementID       pgtype.UUID `json:"engagement_id"`
	TenantID           string      `json:"tenant_id"`
	SubscriptionID     pgtype.Text `json:"subscription_id"`
	PolicyID           string      `json:"policy_id"`
	StatusID           string      `json:"status_id"`
	SshKeyPublic       string      `json:"ssh_key_public"`
	CreationStatus     pgtype.Text `json:"creation_status"`
	SecretsSaved       bool        `json:"secrets_saved"`
	AccountCloudStatus pgtype.Text `json:"account_cloud_status"`
}

func (q *Queries) SaveAzureTenantData(ctx context.Context, arg SaveAzureTenantDataParams) (pgtype.UUID, error) {
	row := q.db.QueryRow(ctx, saveAzureTenantData,
		arg.EngagementID,
		arg.TenantID,
		arg.SubscriptionID,
		arg.PolicyID,
		arg.StatusID,
		arg.SshKeyPublic,
		arg.CreationStatus,
		arg.SecretsSaved,
		arg.AccountCloudStatus,
	)
	var id pgtype.UUID
	err := row.Scan(&id)
	return id, err
}

const setAWSAccountId = `-- name: SetAWSAccountId :exec

UPDATE aws_accounts
SET cloud_account_id = $1
WHERE id = $2
`

type SetAWSAccountIdParams struct {
	CloudAccountID pgtype.Text `json:"cloud_account_id"`
	ID             pgtype.UUID `json:"id"`
}

// -- name: SetEngagementAWSAccountId :exec
// UPDATE aws_accounts
// SET cloud_account_id = $1
// WHERE id = $2;
func (q *Queries) SetAWSAccountId(ctx context.Context, arg SetAWSAccountIdParams) error {
	_, err := q.db.Exec(ctx, setAWSAccountId, arg.CloudAccountID, arg.ID)
	return err
}

const setAWSAccountStatusToError = `-- name: SetAWSAccountStatusToError :exec
UPDATE aws_accounts
SET account_creation_status        = 'ERROR',
    error_message = $1
WHERE id = $2
`

type SetAWSAccountStatusToErrorParams struct {
	ErrorMessage pgtype.Text `json:"error_message"`
	ID           pgtype.UUID `json:"id"`
}

func (q *Queries) SetAWSAccountStatusToError(ctx context.Context, arg SetAWSAccountStatusToErrorParams) error {
	_, err := q.db.Exec(ctx, setAWSAccountStatusToError, arg.ErrorMessage, arg.ID)
	return err
}

const setAWSAccountStatusToSuccess = `-- name: SetAWSAccountStatusToSuccess :exec
UPDATE aws_accounts
SET account_creation_status        = 'SUCCESS'
WHERE id = $1
`

func (q *Queries) SetAWSAccountStatusToSuccess(ctx context.Context, id pgtype.UUID) error {
	_, err := q.db.Exec(ctx, setAWSAccountStatusToSuccess, id)
	return err
}

const setAccountAWSStatus = `-- name: SetAccountAWSStatus :exec
UPDATE aws_accounts
SET account_cloud_status = $1
WHERE id = $2
`

type SetAccountAWSStatusParams struct {
	AccountCloudStatus pgtype.Text `json:"account_cloud_status"`
	ID                 pgtype.UUID `json:"id"`
}

func (q *Queries) SetAccountAWSStatus(ctx context.Context, arg SetAccountAWSStatusParams) error {
	_, err := q.db.Exec(ctx, setAccountAWSStatus, arg.AccountCloudStatus, arg.ID)
	return err
}

const setAzureSubscriptionStatus = `-- name: SetAzureSubscriptionStatus :exec
UPDATE azure_tenants
SET account_cloud_status        = $2
WHERE id = $1
`

type SetAzureSubscriptionStatusParams struct {
	ID                 pgtype.UUID `json:"id"`
	AccountCloudStatus pgtype.Text `json:"account_cloud_status"`
}

func (q *Queries) SetAzureSubscriptionStatus(ctx context.Context, arg SetAzureSubscriptionStatusParams) error {
	_, err := q.db.Exec(ctx, setAzureSubscriptionStatus, arg.ID, arg.AccountCloudStatus)
	return err
}

const setAzureTenantStatusToError = `-- name: SetAzureTenantStatusToError :exec
UPDATE azure_tenants
SET creation_status        = 'ERROR',
    error_message = $1
WHERE id = $2
`

type SetAzureTenantStatusToErrorParams struct {
	ErrorMessage pgtype.Text `json:"error_message"`
	ID           pgtype.UUID `json:"id"`
}

func (q *Queries) SetAzureTenantStatusToError(ctx context.Context, arg SetAzureTenantStatusToErrorParams) error {
	_, err := q.db.Exec(ctx, setAzureTenantStatusToError, arg.ErrorMessage, arg.ID)
	return err
}

const setAzureTenantStatusToSuccess = `-- name: SetAzureTenantStatusToSuccess :exec
UPDATE azure_tenants
SET creation_status        = 'SUCCESS'
WHERE id = $1
`

func (q *Queries) SetAzureTenantStatusToSuccess(ctx context.Context, id pgtype.UUID) error {
	_, err := q.db.Exec(ctx, setAzureTenantStatusToSuccess, id)
	return err
}

const setDeploymentStatusToError = `-- name: SetDeploymentStatusToError :exec
UPDATE deployments as d
SET status        = 'ERROR',
    error_message = $1
WHERE d.id = $2
`

type SetDeploymentStatusToErrorParams struct {
	ErrorMessage pgtype.Text `json:"error_message"`
	ID           pgtype.UUID `json:"id"`
}

func (q *Queries) SetDeploymentStatusToError(ctx context.Context, arg SetDeploymentStatusToErrorParams) error {
	_, err := q.db.Exec(ctx, setDeploymentStatusToError, arg.ErrorMessage, arg.ID)
	return err
}

const setDeploymentStatusToWarning = `-- name: SetDeploymentStatusToWarning :exec
UPDATE deployments as d
SET status        = 'WARNING',
    error_message = $1
WHERE d.id = $2
`

type SetDeploymentStatusToWarningParams struct {
	ErrorMessage pgtype.Text `json:"error_message"`
	ID           pgtype.UUID `json:"id"`
}

func (q *Queries) SetDeploymentStatusToWarning(ctx context.Context, arg SetDeploymentStatusToWarningParams) error {
	_, err := q.db.Exec(ctx, setDeploymentStatusToWarning, arg.ErrorMessage, arg.ID)
	return err
}

const setDomainAssigned = `-- name: SetDomainAssigned :exec
UPDATE domains
SET status = 'ASSIGNED'
WHERE url = $1
`

func (q *Queries) SetDomainAssigned(ctx context.Context, url string) error {
	_, err := q.db.Exec(ctx, setDomainAssigned, url)
	return err
}

const setDomainBurned = `-- name: SetDomainBurned :exec
UPDATE domains
set status = 'BURNED'
WHERE id = $1
`

func (q *Queries) SetDomainBurned(ctx context.Context, id pgtype.UUID) error {
	_, err := q.db.Exec(ctx, setDomainBurned, id)
	return err
}

const setDomainNodeToInactiveOrBurned = `-- name: SetDomainNodeToInactiveOrBurned :exec
UPDATE nodes
SET is_deleted = true
WHERE name = $1
`

func (q *Queries) SetDomainNodeToInactiveOrBurned(ctx context.Context, name string) error {
	_, err := q.db.Exec(ctx, setDomainNodeToInactiveOrBurned, name)
	return err
}

const setDomainQuarantine = `-- name: SetDomainQuarantine :exec
UPDATE domains
set status = 'QUARANTINE'
WHERE url = $1
`

func (q *Queries) SetDomainQuarantine(ctx context.Context, url string) error {
	_, err := q.db.Exec(ctx, setDomainQuarantine, url)
	return err
}

const setDomainUnAssigned = `-- name: SetDomainUnAssigned :exec
UPDATE domains
set status = 'UNASSIGNED'
WHERE url = $1
`

func (q *Queries) SetDomainUnAssigned(ctx context.Context, url string) error {
	_, err := q.db.Exec(ctx, setDomainUnAssigned, url)
	return err
}

const setEngagementStatusToError = `-- name: SetEngagementStatusToError :exec
UPDATE engagements
SET status        = 'ERROR',
    error_message = $1
WHERE id = $2
`

type SetEngagementStatusToErrorParams struct {
	ErrorMessage pgtype.Text `json:"error_message"`
	ID           pgtype.UUID `json:"id"`
}

func (q *Queries) SetEngagementStatusToError(ctx context.Context, arg SetEngagementStatusToErrorParams) error {
	_, err := q.db.Exec(ctx, setEngagementStatusToError, arg.ErrorMessage, arg.ID)
	return err
}

const setEngagementStatusToSuccess = `-- name: SetEngagementStatusToSuccess :exec
UPDATE engagements
SET status = 'SUCCESS'
WHERE id = $1
`

func (q *Queries) SetEngagementStatusToSuccess(ctx context.Context, id pgtype.UUID) error {
	_, err := q.db.Exec(ctx, setEngagementStatusToSuccess, id)
	return err
}

const setMissingCloudInstancesAsTerminated = `-- name: SetMissingCloudInstancesAsTerminated :exec
UPDATE node_type_cloud_instances ntci
SET cloud_instance_state = 'terminated'
WHERE cloud_instance_id = ANY($1::text[])
`

func (q *Queries) SetMissingCloudInstancesAsTerminated(ctx context.Context, dollar_1 []string) error {
	_, err := q.db.Exec(ctx, setMissingCloudInstancesAsTerminated, dollar_1)
	return err
}

const setUserSshKey = `-- name: SetUserSshKey :one
UPDATE users
SET ssh_key               = $1,
    ssh_key_label         = $2,
    ssh_key_creation_date = NOW()
WHERE id = $3
RETURNING id, username, custom_username, full_name, app_role, ssh_key, ssh_key_label, ssh_key_creation_date, is_inactive
`

type SetUserSshKeyParams struct {
	SshKey      pgtype.Text `json:"ssh_key"`
	SshKeyLabel pgtype.Text `json:"ssh_key_label"`
	ID          pgtype.UUID `json:"id"`
}

func (q *Queries) SetUserSshKey(ctx context.Context, arg SetUserSshKeyParams) (User, error) {
	row := q.db.QueryRow(ctx, setUserSshKey, arg.SshKey, arg.SshKeyLabel, arg.ID)
	var i User
	err := row.Scan(
		&i.ID,
		&i.Username,
		&i.CustomUsername,
		&i.FullName,
		&i.AppRole,
		&i.SshKey,
		&i.SshKeyLabel,
		&i.SshKeyCreationDate,
		&i.IsInactive,
	)
	return i, err
}

const syncPrioritizedRegions = `-- name: SyncPrioritizedRegions :exec
WITH new_values (name) AS (
    -- Values to be inserted/updated
    SELECT unnest($1::text[])
),
cleanup AS (
    DELETE FROM prioritized_regions
    WHERE NOT EXISTS (
        SELECT 1 FROM unnest($1::text[]) AS new_name
        WHERE prioritized_regions.name = new_name
    )
),
insertions AS (
    INSERT INTO prioritized_regions (name)
    SELECT name FROM new_values
    WHERE NOT EXISTS (
        SELECT 1 FROM prioritized_regions
        WHERE prioritized_regions.name = new_values.name
    )
)
SELECT 1
`

// Delete records that exist in the table but not in the new values
// If $1 is empty, this will delete all records since the subquery returns no rows
// Insert new records that don't already exist
func (q *Queries) SyncPrioritizedRegions(ctx context.Context, dollar_1 []string) error {
	_, err := q.db.Exec(ctx, syncPrioritizedRegions, dollar_1)
	return err
}

const updateAzureTenantSecretsStatus = `-- name: UpdateAzureTenantSecretsStatus :exec
UPDATE azure_tenants
SET secrets_saved        = 'TRUE'
WHERE id = $1
`

func (q *Queries) UpdateAzureTenantSecretsStatus(ctx context.Context, id pgtype.UUID) error {
	_, err := q.db.Exec(ctx, updateAzureTenantSecretsStatus, id)
	return err
}

const updateCloudInstanceAWSState = `-- name: UpdateCloudInstanceAWSState :exec
UPDATE node_type_cloud_instances ntci
SET cloud_instance_state        = $1
WHERE ntci.cloud_instance_id = $2
`

type UpdateCloudInstanceAWSStateParams struct {
	CloudInstanceState NullCiStateEnum `json:"cloud_instance_state"`
	CloudInstanceID    pgtype.Text     `json:"cloud_instance_id"`
}

func (q *Queries) UpdateCloudInstanceAWSState(ctx context.Context, arg UpdateCloudInstanceAWSStateParams) error {
	_, err := q.db.Exec(ctx, updateCloudInstanceAWSState, arg.CloudInstanceState, arg.CloudInstanceID)
	return err
}

const updateCloudInstanceAWSStateByAwsAccountAndID = `-- name: UpdateCloudInstanceAWSStateByAwsAccountAndID :one
UPDATE node_type_cloud_instances
SET cloud_instance_state = $1
WHERE aws_account_id = $2 AND cloud_instance_id = $3
RETURNING 1
`

type UpdateCloudInstanceAWSStateByAwsAccountAndIDParams struct {
	CloudInstanceState NullCiStateEnum `json:"cloud_instance_state"`
	AwsAccountID       pgtype.UUID     `json:"aws_account_id"`
	CloudInstanceID    pgtype.Text     `json:"cloud_instance_id"`
}

func (q *Queries) UpdateCloudInstanceAWSStateByAwsAccountAndID(ctx context.Context, arg UpdateCloudInstanceAWSStateByAwsAccountAndIDParams) (int32, error) {
	row := q.db.QueryRow(ctx, updateCloudInstanceAWSStateByAwsAccountAndID, arg.CloudInstanceState, arg.AwsAccountID, arg.CloudInstanceID)
	var column_1 int32
	err := row.Scan(&column_1)
	return column_1, err
}

const updateCloudInstanceAWSStateByNodeAndID = `-- name: UpdateCloudInstanceAWSStateByNodeAndID :one
UPDATE node_type_cloud_instances
SET cloud_instance_state = $1
WHERE node_id = $2 AND cloud_instance_id = $3
RETURNING 1
`

type UpdateCloudInstanceAWSStateByNodeAndIDParams struct {
	CloudInstanceState NullCiStateEnum `json:"cloud_instance_state"`
	NodeID             pgtype.UUID     `json:"node_id"`
	CloudInstanceID    pgtype.Text     `json:"cloud_instance_id"`
}

func (q *Queries) UpdateCloudInstanceAWSStateByNodeAndID(ctx context.Context, arg UpdateCloudInstanceAWSStateByNodeAndIDParams) (int32, error) {
	row := q.db.QueryRow(ctx, updateCloudInstanceAWSStateByNodeAndID, arg.CloudInstanceState, arg.NodeID, arg.CloudInstanceID)
	var column_1 int32
	err := row.Scan(&column_1)
	return column_1, err
}

const updateCloudInstanceAzureState = `-- name: UpdateCloudInstanceAzureState :exec
UPDATE node_type_cloud_instances ntci
SET cloud_instance_state        = $1
WHERE ntci.cloud_instance_id = $2
`

type UpdateCloudInstanceAzureStateParams struct {
	CloudInstanceState NullCiStateEnum `json:"cloud_instance_state"`
	CloudInstanceID    pgtype.Text     `json:"cloud_instance_id"`
}

func (q *Queries) UpdateCloudInstanceAzureState(ctx context.Context, arg UpdateCloudInstanceAzureStateParams) error {
	_, err := q.db.Exec(ctx, updateCloudInstanceAzureState, arg.CloudInstanceState, arg.CloudInstanceID)
	return err
}

const updateCloudInstanceIPByNodeAndID = `-- name: UpdateCloudInstanceIPByNodeAndID :one
UPDATE node_type_cloud_instances
SET public_ipv4_address = $1
WHERE node_id = $2 AND cloud_instance_id = $3
RETURNING 1
`

type UpdateCloudInstanceIPByNodeAndIDParams struct {
	PublicIpv4Address *netip.Addr `json:"public_ipv4_address"`
	NodeID            pgtype.UUID `json:"node_id"`
	CloudInstanceID   pgtype.Text `json:"cloud_instance_id"`
}

func (q *Queries) UpdateCloudInstanceIPByNodeAndID(ctx context.Context, arg UpdateCloudInstanceIPByNodeAndIDParams) (int32, error) {
	row := q.db.QueryRow(ctx, updateCloudInstanceIPByNodeAndID, arg.PublicIpv4Address, arg.NodeID, arg.CloudInstanceID)
	var column_1 int32
	err := row.Scan(&column_1)
	return column_1, err
}

const updateCloudInstanceIpv4AddressAndID = `-- name: UpdateCloudInstanceIpv4AddressAndID :exec
UPDATE node_type_cloud_instances
SET public_ipv4_address = $2,
    cloud_instance_id = $3
WHERE node_id = $1
`

type UpdateCloudInstanceIpv4AddressAndIDParams struct {
	NodeID            pgtype.UUID `json:"node_id"`
	PublicIpv4Address *netip.Addr `json:"public_ipv4_address"`
	CloudInstanceID   pgtype.Text `json:"cloud_instance_id"`
}

func (q *Queries) UpdateCloudInstanceIpv4AddressAndID(ctx context.Context, arg UpdateCloudInstanceIpv4AddressAndIDParams) error {
	_, err := q.db.Exec(ctx, updateCloudInstanceIpv4AddressAndID, arg.NodeID, arg.PublicIpv4Address, arg.CloudInstanceID)
	return err
}

const updateDeploymentStatus = `-- name: UpdateDeploymentStatus :exec
UPDATE deployments as d
SET status = $1
WHERE d.id = $2
`

type UpdateDeploymentStatusParams struct {
	Status DeploymentStatusEnum `json:"status"`
	ID     pgtype.UUID          `json:"id"`
}

func (q *Queries) UpdateDeploymentStatus(ctx context.Context, arg UpdateDeploymentStatusParams) error {
	_, err := q.db.Exec(ctx, updateDeploymentStatus, arg.Status, arg.ID)
	return err
}

const updateDomain = `-- name: UpdateDomain :one
UPDATE domains
SET
    url = $2,
    registrar = $3,
    purchase_date = $4,
    renewal_date = $5,
    status = $6,
    engagement = $7,
    client = $8,
    age = $9
WHERE id = $1
RETURNING id, url, registrar, purchase_date, renewal_date, status, engagement, client, age, created_at
`

type UpdateDomainParams struct {
	ID           pgtype.UUID          `json:"id"`
	Url          string               `json:"url"`
	Registrar    pgtype.Text          `json:"registrar"`
	PurchaseDate pgtype.Date          `json:"purchase_date"`
	RenewalDate  pgtype.Date          `json:"renewal_date"`
	Status       NullDomainStatusEnum `json:"status"`
	Engagement   pgtype.Text          `json:"engagement"`
	Client       pgtype.Text          `json:"client"`
	Age          pgtype.Int4          `json:"age"`
}

func (q *Queries) UpdateDomain(ctx context.Context, arg UpdateDomainParams) (Domain, error) {
	row := q.db.QueryRow(ctx, updateDomain,
		arg.ID,
		arg.Url,
		arg.Registrar,
		arg.PurchaseDate,
		arg.RenewalDate,
		arg.Status,
		arg.Engagement,
		arg.Client,
		arg.Age,
	)
	var i Domain
	err := row.Scan(
		&i.ID,
		&i.Url,
		&i.Registrar,
		&i.PurchaseDate,
		&i.RenewalDate,
		&i.Status,
		&i.Engagement,
		&i.Client,
		&i.Age,
		&i.CreatedAt,
	)
	return i, err
}

const updateDomainEngagementClient = `-- name: UpdateDomainEngagementClient :exec
UPDATE domains
SET
    engagement = $2,
    client = $3,
    status = $4
WHERE id = $1
RETURNING id, url, registrar, purchase_date, renewal_date, status, engagement, client, age, created_at
`

type UpdateDomainEngagementClientParams struct {
	ID         pgtype.UUID          `json:"id"`
	Engagement pgtype.Text          `json:"engagement"`
	Client     pgtype.Text          `json:"client"`
	Status     NullDomainStatusEnum `json:"status"`
}

func (q *Queries) UpdateDomainEngagementClient(ctx context.Context, arg UpdateDomainEngagementClientParams) error {
	_, err := q.db.Exec(ctx, updateDomainEngagementClient,
		arg.ID,
		arg.Engagement,
		arg.Client,
		arg.Status,
	)
	return err
}

const updateDomainField = `-- name: UpdateDomainField :one
UPDATE domains
SET
    url = CASE WHEN $2 = 'url' THEN $3 ELSE url END,
    registrar = CASE WHEN $2 = 'registrar' THEN $3 ELSE registrar END,
    purchase_date = CASE WHEN $2 = 'purchase_date' THEN $3::date ELSE purchase_date END,
    renewal_date = CASE WHEN $2 = 'renewal_date' THEN $3::date ELSE renewal_date END,
    status = CASE WHEN $2 = 'status' THEN $3::domain_status_enum ELSE status END,
    engagement = CASE WHEN $2 = 'engagement' THEN $3 ELSE engagement END,
    client = CASE WHEN $2 = 'client' THEN $3 ELSE client END,
    age = CASE WHEN $2 = 'age' THEN $3::integer ELSE age END
WHERE id = $1
RETURNING id, url, registrar, purchase_date, renewal_date, status, engagement, client, age, created_at
`

type UpdateDomainFieldParams struct {
	ID      pgtype.UUID `json:"id"`
	Column2 interface{} `json:"column_2"`
	Url     string      `json:"url"`
}

func (q *Queries) UpdateDomainField(ctx context.Context, arg UpdateDomainFieldParams) (Domain, error) {
	row := q.db.QueryRow(ctx, updateDomainField, arg.ID, arg.Column2, arg.Url)
	var i Domain
	err := row.Scan(
		&i.ID,
		&i.Url,
		&i.Registrar,
		&i.PurchaseDate,
		&i.RenewalDate,
		&i.Status,
		&i.Engagement,
		&i.Client,
		&i.Age,
		&i.CreatedAt,
	)
	return i, err
}

const updateInstanceTypes = `-- name: UpdateInstanceTypes :exec
INSERT INTO instance_size_mappings (
    provider,
    size_alias,
    priority,
    instance_type
) VALUES (
    $1, $2, $3, $4
)
ON CONFLICT (provider, size_alias, priority)
DO UPDATE SET
    instance_type = EXCLUDED.instance_type
RETURNING id, provider, size_alias, priority, instance_type
`

type UpdateInstanceTypesParams struct {
	Provider     ProviderEnum `json:"provider"`
	SizeAlias    string       `json:"size_alias"`
	Priority     int32        `json:"priority"`
	InstanceType string       `json:"instance_type"`
}

func (q *Queries) UpdateInstanceTypes(ctx context.Context, arg UpdateInstanceTypesParams) error {
	_, err := q.db.Exec(ctx, updateInstanceTypes,
		arg.Provider,
		arg.SizeAlias,
		arg.Priority,
		arg.InstanceType,
	)
	return err
}

const updateNodeGroup = `-- name: UpdateNodeGroup :one
UPDATE node_groups ng
SET name = $1
FROM engagements e
         JOIN engagements_users eu ON e.id = eu.engagement_id
         JOIN users u ON eu.user_id = u.id
WHERE ng.engagement_id = e.id
  AND ng.id = $2
  AND u.id = $3
RETURNING e.id, title, wbs_code, e.is_active, client_id, e.created_at, e.updated_at, status, error_message, eu.engagement_id, user_id, u.id, username, custom_username, full_name, app_role, ssh_key, ssh_key_label, ssh_key_creation_date, is_inactive, ng.id, name, ng.is_active, ng.created_at, ng.updated_at, ng.engagement_id
`

type UpdateNodeGroupParams struct {
	Name string      `json:"name"`
	ID   pgtype.UUID `json:"id"`
	ID_2 pgtype.UUID `json:"id_2"`
}

type UpdateNodeGroupRow struct {
	ID                 pgtype.UUID          `json:"id"`
	Title              string               `json:"title"`
	WbsCode            string               `json:"wbs_code"`
	IsActive           bool                 `json:"is_active"`
	ClientID           pgtype.UUID          `json:"client_id"`
	CreatedAt          pgtype.Timestamp     `json:"created_at"`
	UpdatedAt          pgtype.Timestamp     `json:"updated_at"`
	Status             EngagementStatusEnum `json:"status"`
	ErrorMessage       pgtype.Text          `json:"error_message"`
	EngagementID       pgtype.UUID          `json:"engagement_id"`
	UserID             pgtype.UUID          `json:"user_id"`
	ID_2               pgtype.UUID          `json:"id_2"`
	Username           string               `json:"username"`
	CustomUsername     pgtype.Text          `json:"custom_username"`
	FullName           pgtype.Text          `json:"full_name"`
	AppRole            pgtype.Text          `json:"app_role"`
	SshKey             pgtype.Text          `json:"ssh_key"`
	SshKeyLabel        pgtype.Text          `json:"ssh_key_label"`
	SshKeyCreationDate pgtype.Timestamp     `json:"ssh_key_creation_date"`
	IsInactive         pgtype.Bool          `json:"is_inactive"`
	ID_3               pgtype.UUID          `json:"id_3"`
	Name               string               `json:"name"`
	IsActive_2         bool                 `json:"is_active_2"`
	CreatedAt_2        pgtype.Timestamp     `json:"created_at_2"`
	UpdatedAt_2        pgtype.Timestamp     `json:"updated_at_2"`
	EngagementID_2     pgtype.UUID          `json:"engagement_id_2"`
}

func (q *Queries) UpdateNodeGroup(ctx context.Context, arg UpdateNodeGroupParams) (UpdateNodeGroupRow, error) {
	row := q.db.QueryRow(ctx, updateNodeGroup, arg.Name, arg.ID, arg.ID_2)
	var i UpdateNodeGroupRow
	err := row.Scan(
		&i.ID,
		&i.Title,
		&i.WbsCode,
		&i.IsActive,
		&i.ClientID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Status,
		&i.ErrorMessage,
		&i.EngagementID,
		&i.UserID,
		&i.ID_2,
		&i.Username,
		&i.CustomUsername,
		&i.FullName,
		&i.AppRole,
		&i.SshKey,
		&i.SshKeyLabel,
		&i.SshKeyCreationDate,
		&i.IsInactive,
		&i.ID_3,
		&i.Name,
		&i.IsActive_2,
		&i.CreatedAt_2,
		&i.UpdatedAt_2,
		&i.EngagementID_2,
	)
	return i, err
}

const updateNodeGroupId = `-- name: UpdateNodeGroupId :exec
UPDATE nodes
SET node_group_id = $1
WHERE id = ANY ($2::uuid[])
`

type UpdateNodeGroupIdParams struct {
	NodeGroupID pgtype.UUID   `json:"node_group_id"`
	Column2     []pgtype.UUID `json:"column_2"`
}

func (q *Queries) UpdateNodeGroupId(ctx context.Context, arg UpdateNodeGroupIdParams) error {
	_, err := q.db.Exec(ctx, updateNodeGroupId, arg.NodeGroupID, arg.Column2)
	return err
}

const updateNodeName = `-- name: UpdateNodeName :exec
UPDATE nodes
SET name = $1
WHERE id = $2
`

type UpdateNodeNameParams struct {
	Name string      `json:"name"`
	ID   pgtype.UUID `json:"id"`
}

func (q *Queries) UpdateNodeName(ctx context.Context, arg UpdateNodeNameParams) error {
	_, err := q.db.Exec(ctx, updateNodeName, arg.Name, arg.ID)
	return err
}

const updateNodeNodeGroup = `-- name: UpdateNodeNodeGroup :exec
UPDATE nodes
SET node_group_id = $2
WHERE id = $1
`

type UpdateNodeNodeGroupParams struct {
	ID          pgtype.UUID `json:"id"`
	NodeGroupID pgtype.UUID `json:"node_group_id"`
}

func (q *Queries) UpdateNodeNodeGroup(ctx context.Context, arg UpdateNodeNodeGroupParams) error {
	_, err := q.db.Exec(ctx, updateNodeNodeGroup, arg.ID, arg.NodeGroupID)
	return err
}

const updateNodeTypeCloudInstance = `-- name: UpdateNodeTypeCloudInstance :one
UPDATE node_type_cloud_instances
SET name       = $2,
    open_ports = $3
WHERE node_id = $1
RETURNING provider, region, operating_system_image_id, instance_type, name, open_ports, public_ipv4_address, node_id, cloud_instance_state, cloud_instance_id, aws_account_id, azure_tenant_id
`

type UpdateNodeTypeCloudInstanceParams struct {
	NodeID    pgtype.UUID `json:"node_id"`
	Name      string      `json:"name"`
	OpenPorts []int32     `json:"open_ports"`
}

func (q *Queries) UpdateNodeTypeCloudInstance(ctx context.Context, arg UpdateNodeTypeCloudInstanceParams) (NodeTypeCloudInstance, error) {
	row := q.db.QueryRow(ctx, updateNodeTypeCloudInstance, arg.NodeID, arg.Name, arg.OpenPorts)
	var i NodeTypeCloudInstance
	err := row.Scan(
		&i.Provider,
		&i.Region,
		&i.OperatingSystemImageID,
		&i.InstanceType,
		&i.Name,
		&i.OpenPorts,
		&i.PublicIpv4Address,
		&i.NodeID,
		&i.CloudInstanceState,
		&i.CloudInstanceID,
		&i.AwsAccountID,
		&i.AzureTenantID,
	)
	return i, err
}

const updateNodeTypeEmailAddress = `-- name: UpdateNodeTypeEmailAddress :one
UPDATE node_type_email_addresses
SET email_address = $1
WHERE node_id = $2
RETURNING email_address, node_id
`

type UpdateNodeTypeEmailAddressParams struct {
	EmailAddress string      `json:"email_address"`
	NodeID       pgtype.UUID `json:"node_id"`
}

func (q *Queries) UpdateNodeTypeEmailAddress(ctx context.Context, arg UpdateNodeTypeEmailAddressParams) (NodeTypeEmailAddress, error) {
	row := q.db.QueryRow(ctx, updateNodeTypeEmailAddress, arg.EmailAddress, arg.NodeID)
	var i NodeTypeEmailAddress
	err := row.Scan(&i.EmailAddress, &i.NodeID)
	return i, err
}

const updateNodeTypeHost = `-- name: UpdateNodeTypeHost :one
UPDATE node_type_hosts
SET name              = $2,
    ip_addresses      = $3,
    alternative_names = $4
WHERE node_id = $1
RETURNING name, ip_addresses, alternative_names, node_id
`

type UpdateNodeTypeHostParams struct {
	NodeID           pgtype.UUID  `json:"node_id"`
	Name             string       `json:"name"`
	IpAddresses      []netip.Addr `json:"ip_addresses"`
	AlternativeNames []string     `json:"alternative_names"`
}

func (q *Queries) UpdateNodeTypeHost(ctx context.Context, arg UpdateNodeTypeHostParams) (NodeTypeHost, error) {
	row := q.db.QueryRow(ctx, updateNodeTypeHost,
		arg.NodeID,
		arg.Name,
		arg.IpAddresses,
		arg.AlternativeNames,
	)
	var i NodeTypeHost
	err := row.Scan(
		&i.Name,
		&i.IpAddresses,
		&i.AlternativeNames,
		&i.NodeID,
	)
	return i, err
}

const updateNodeTypePerson = `-- name: UpdateNodeTypePerson :one
UPDATE node_type_persons
SET first_name = $1,
    last_name  = $2,
    email      = $3,
    company    = $4,
    title      = $5
WHERE node_id = $6
RETURNING first_name, last_name, email, company, title, node_id
`

type UpdateNodeTypePersonParams struct {
	FirstName string      `json:"first_name"`
	LastName  pgtype.Text `json:"last_name"`
	Email     pgtype.Text `json:"email"`
	Company   pgtype.Text `json:"company"`
	Title     pgtype.Text `json:"title"`
	NodeID    pgtype.UUID `json:"node_id"`
}

func (q *Queries) UpdateNodeTypePerson(ctx context.Context, arg UpdateNodeTypePersonParams) (NodeTypePerson, error) {
	row := q.db.QueryRow(ctx, updateNodeTypePerson,
		arg.FirstName,
		arg.LastName,
		arg.Email,
		arg.Company,
		arg.Title,
		arg.NodeID,
	)
	var i NodeTypePerson
	err := row.Scan(
		&i.FirstName,
		&i.LastName,
		&i.Email,
		&i.Company,
		&i.Title,
		&i.NodeID,
	)
	return i, err
}

const updateNodeTypeUrl = `-- name: UpdateNodeTypeUrl :one
UPDATE node_type_urls
SET url = $1
WHERE node_id = $2
RETURNING url, node_id
`

type UpdateNodeTypeUrlParams struct {
	Url    string      `json:"url"`
	NodeID pgtype.UUID `json:"node_id"`
}

func (q *Queries) UpdateNodeTypeUrl(ctx context.Context, arg UpdateNodeTypeUrlParams) (NodeTypeUrl, error) {
	row := q.db.QueryRow(ctx, updateNodeTypeUrl, arg.Url, arg.NodeID)
	var i NodeTypeUrl
	err := row.Scan(&i.Url, &i.NodeID)
	return i, err
}

const updateNodeTypeUrlByNodeID = `-- name: UpdateNodeTypeUrlByNodeID :exec
UPDATE node_type_urls
SET url = $2
WHERE node_id = $1
`

type UpdateNodeTypeUrlByNodeIDParams struct {
	NodeID pgtype.UUID `json:"node_id"`
	Url    string      `json:"url"`
}

func (q *Queries) UpdateNodeTypeUrlByNodeID(ctx context.Context, arg UpdateNodeTypeUrlByNodeIDParams) error {
	_, err := q.db.Exec(ctx, updateNodeTypeUrlByNodeID, arg.NodeID, arg.Url)
	return err
}

const updateUser = `-- name: UpdateUser :exec
UPDATE users
SET full_name = $2,
    app_role = $3
WHERE id = $1
`

type UpdateUserParams struct {
	ID       pgtype.UUID `json:"id"`
	FullName pgtype.Text `json:"full_name"`
	AppRole  pgtype.Text `json:"app_role"`
}

func (q *Queries) UpdateUser(ctx context.Context, arg UpdateUserParams) error {
	_, err := q.db.Exec(ctx, updateUser, arg.ID, arg.FullName, arg.AppRole)
	return err
}

const updateUserUsername = `-- name: UpdateUserUsername :exec
UPDATE users
SET custom_username = $1
WHERE id = $2
`

type UpdateUserUsernameParams struct {
	CustomUsername pgtype.Text `json:"custom_username"`
	ID             pgtype.UUID `json:"id"`
}

func (q *Queries) UpdateUserUsername(ctx context.Context, arg UpdateUserUsernameParams) error {
	_, err := q.db.Exec(ctx, updateUserUsername, arg.CustomUsername, arg.ID)
	return err
}
